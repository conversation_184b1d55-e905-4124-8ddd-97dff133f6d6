{% if boxesPage.custom_config.show_cta_banner and not input('bedankt') == '1' %}
    <div id="cta_banner" class="lg:hidden fixed inset-x-0 bottom-0 py-5 bg-white shadow border-t z-[49]"
        x-show="ctaBanner"
        x-cloak
        x-transition:enter="ease-out duration-300"
        x-transition:enter-start="opacity-0 translate-y-full"
        x-transition:enter-end="opacity-100 translate-y-0"
        x-transition:leave="ease-in duration-200"
        x-transition:leave-start="opacity-100 translate-y-0"
        x-transition:leave-end="opacity-0 translate-y-full">

        <div class="container">
            <div class="flex justify-between items-center gap-4">
                <div class="flex-1 text-sm">
                    <p>{{ boxesPage.custom_config.ctabanner_text }}</p>
                </div>
                <div class="shrink-0">
                    <button type"button" class="{{ boxesPage.custom_config.ctabanner_active_first_field ? 'offerLink' : 'offerLinkWithoutFocus' }} bg-apple-600 hover:bg-apple-500 border-b-2 border-apple-800 hover:border-apple-700 py-2 px-6 rounded text-white font-medium text-sm" @click="formFocus = true">
                        {{ boxesPage.custom_config.ctabanner_buttontext }}
                    </button>
                </div>
            </div>
        </div>

    </div>
{% endif %}
