<?php namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

/**
 * Consolidated fieldtype seeder for VBO Settings Plugin v2.0
 * This seeder includes all fieldtype creations and updates from versions 1.0.1 through 1.3.3
 */
class SeedAllFieldTypes extends Seeder
{
    public function run()
    {
        $path = __DIR__.'/fields/';
        
        // Define all fieldtypes that need to be created or updated
        $fieldTypes = [
            // Alert/Warning fields
            [
                'name' => 'Melding blok',
                'code' => 'melding-blok',
                'markup' => '_alert.htm'
            ],
            [
                'name' => 'Waarschuwing',
                'code' => 'waarschuwing',
                'markup' => '_waarschuwing.htm'
            ],
            
            // Address and location fields
            [
                'name' => 'Adresvinder',
                'code' => 'adresvinder',
                'markup' => '_adresvinder.htm'
            ],
            [
                'name' => 'Postcode suggesties',
                'code' => 'postcode-sug',
                'markup' => '_postcode_sug.htm'
            ],
            [
                'name' => 'Straat suggesties',
                'code' => 'straat-sug',
                'markup' => '_straat_sug.htm'
            ],
            [
                'name' => 'Plaats suggesties',
                'code' => 'plaats-sug',
                'markup' => '_plaats_sug.htm'
            ],
            [
                'name' => 'Huisnummer suggesties',
                'code' => 'housenumber-sug',
                'markup' => '_housenumber_sug.htm'
            ],
            [
                'name' => 'Straatnaam dropdown',
                'code' => 'straatnaam-dropdown',
                'markup' => '_straatnaam_dropdown.htm'
            ],
            [
                'name' => 'Nieuwsbrief postcode',
                'code' => 'nieuwsbrief-postcode',
                'markup' => '_nieuwsbrief_postcode.htm'
            ],
            
            // Phone and contact fields
            [
                'name' => 'Telefoonnummer OTP',
                'code' => 'telefoonnummer-otp',
                'markup' => '_telefoonnummer_otp.htm'
            ],
            [
                'name' => 'Telefoonnummer OTP 2',
                'code' => 'telefoonnummer-otp-2',
                'markup' => '_telefoonnummer_otp_2.htm'
            ],
            [
                'name' => 'Internationaal telefoonnummer',
                'code' => 'telephone-int',
                'markup' => '_telephone_int.htm'
            ],
            
            // Name fields
            [
                'name' => 'Voornaam Achternaam',
                'code' => 'firstname-lastname',
                'markup' => '_firstname_lastname.htm'
            ],
            [
                'name' => 'Achternaam Voornamen',
                'code' => 'achternaam-voornamen',
                'markup' => '_achternaam_voornamen.htm'
            ],
            [
                'name' => 'Achternaam Initialen',
                'code' => 'achternaam-initialen',
                'markup' => '_achternaam_initialen.htm'
            ],
            
            // Special input fields
            [
                'name' => 'Verborgen veld',
                'code' => 'hidden',
                'markup' => '_hidden.htm'
            ],
            [
                'name' => 'Text Number',
                'code' => 'text-number',
                'markup' => '_text_number.htm'
            ],
            [
                'name' => 'Begin knop',
                'code' => 'begin-knop',
                'markup' => '_begin_knop.htm'
            ],
            
            // Date fields
            [
                'name' => 'Geboortedatum alternatief',
                'code' => 'dob-alt',
                'markup' => '_dob_alt.htm'
            ],
            
            // Financial fields
            [
                'name' => 'Hypotheekrente periode',
                'code' => 'hypotheekrente-periode',
                'markup' => '_hypotheekrente_periode.htm'
            ],
            [
                'name' => 'Hypotheekrente verhouding',
                'code' => 'hypotheekrente-verhouding',
                'markup' => '_hypotheekrente_verhouding.htm'
            ],
            
            // Belgium specific fields
            [
                'name' => 'Postcode België',
                'code' => 'zipcode-be',
                'markup' => '_zipcode_city.htm'
            ],
            
            // Prelander fields
            [
                'name' => 'Prelander radio',
                'code' => 'prelander-radio',
                'markup' => '_prelander_radio.htm'
            ],
            
            // Email fields
            [
                'name' => 'E-mail nieuwsbrief',
                'code' => 'email-newsletter',
                'markup' => '_email_newsletter.htm'
            ]
        ];
        
        // Create or update each fieldtype
        foreach ($fieldTypes as $fieldTypeData) {
            $this->createOrUpdateFieldType($fieldTypeData, $path);
        }
    }
    
    private function createOrUpdateFieldType($data, $path)
    {
        $fieldType = FieldType::where('code', $data['code'])->first();
        
        if (!$fieldType) {
            // Create new fieldtype
            FieldType::create([
                'name' => $data['name'],
                'code' => $data['code'],
                'markup' => File::get($path . $data['markup']),
            ]);
        } else {
            // Update existing fieldtype
            $fieldType->name = $data['name'];
            $fieldType->markup = File::get($path . $data['markup']);
            $fieldType->save();
        }
    }
}
