# Legacy Update Bestanden - V<PERSON> Settings Plugin

Deze directory bevat alle legacy update bestanden van VBO Settings plugin versies 1.0.1 tot 1.3.3.

Deze bestanden zijn automatisch gearchiveerd tijdens de upgrade naar versie 2.0 en zijn niet meer nodig voor de werking van de plugin.

## Waarom gearchiveerd?

Versie 2.0 consolideert alle functionaliteit van deze 118 individuele update bestanden in 4 geconsolideerde bestanden:

- `create_consolidated_schema.php` - Alle database schema wijzigingen
- `seed_all_fieldtypes.php` - Alle fieldtype definities
- `seed_all_roles.php` - Alle gebruikersrollen en permissies
- `set_all_configurations.php` - Alle configuratie instellingen

## Veiligheid

Deze bestanden kunnen veilig worden verwijderd, maar worden bewaard voor:
- Referentie doeleinden
- Debugging van legacy functionaliteit
- Rollback scenario's (indien nodig)

## Automatisch gearchiveerd op

2025-11-27 13:35:46

## Totaal aantal gearchiveerde bestanden

118 bestanden

## Rollback instructies

Als er problemen zijn met versie 2.0, kun je de legacy bestanden terugzetten:

```bash
# Verplaats bestanden terug
mv plugins/vbo/settings/updates/legacy/* plugins/vbo/settings/updates/
# Verwijder de geconsolideerde bestanden
rm plugins/vbo/settings/updates/create_consolidated_schema.php
rm plugins/vbo/settings/updates/seed_all_fieldtypes.php
rm plugins/vbo/settings/updates/seed_all_roles.php
rm plugins/vbo/settings/updates/set_all_configurations.php
# Update version.yaml terug naar 1.3.3
```
