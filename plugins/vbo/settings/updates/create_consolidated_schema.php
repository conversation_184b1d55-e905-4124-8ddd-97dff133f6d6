<?php namespace Vbo\Settings\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

/**
 * Consolidated schema migration for VBO Settings Plugin v2.0
 * This migration includes all database schema changes from versions 1.0.1 through 1.3.3
 */
class CreateConsolidatedSchema extends Migration
{
    public function up()
    {
        // Add fields to renatio_formbuilder_fields table
        if (Schema::hasTable('renatio_formbuilder_fields')) {
            Schema::table('renatio_formbuilder_fields', function($table) {
                // Conditional logic fields (from create_cl_fields.php)
                if (!Schema::hasColumn('renatio_formbuilder_fields', 'conditional_logic')) {
                    $table->boolean('conditional_logic')->default(false);
                }
                if (!Schema::hasColumn('renatio_formbuilder_fields', 'cl_action')) {
                    $table->string('cl_action')->nullable();
                }
                if (!Schema::hasColumn('renatio_formbuilder_fields', 'cl_field')) {
                    $table->string('cl_field')->nullable();
                }
                if (!Schema::hasColumn('renatio_formbuilder_fields', 'cl_condition')) {
                    $table->string('cl_condition')->nullable();
                }
                if (!Schema::hasColumn('renatio_formbuilder_fields', 'cl_value')) {
                    $table->string('cl_value')->nullable();
                }

                // Form builder field extensions
                if (!Schema::hasColumn('renatio_formbuilder_fields', 'description')) {
                    $table->text('description')->nullable();
                }
                if (!Schema::hasColumn('renatio_formbuilder_fields', 'range')) {
                    $table->string('range')->nullable();
                }
                if (!Schema::hasColumn('renatio_formbuilder_fields', 'offer_example')) {
                    $table->boolean('offer_example')->default(false);
                }
                if (!Schema::hasColumn('renatio_formbuilder_fields', 'required_field')) {
                    $table->boolean('required_field')->default(false);
                }
                if (!Schema::hasColumn('renatio_formbuilder_fields', 'hide_label')) {
                    $table->boolean('hide_label')->default(false);
                }
                if (!Schema::hasColumn('renatio_formbuilder_fields', 'custom_validation')) {
                    $table->string('custom_validation')->nullable();
                }
            });
        }

        // Add fields to renatio_formbuilder_forms table
        if (Schema::hasTable('renatio_formbuilder_forms')) {
            Schema::table('renatio_formbuilder_forms', function($table) {
                // Conversion pixels
                if (!Schema::hasColumn('renatio_formbuilder_forms', 'converse_pixels')) {
                    $table->text('converse_pixels')->nullable();
                }

                // UTM and postback fields
                if (!Schema::hasColumn('renatio_formbuilder_forms', 'utm_source')) {
                    $table->string('utm_source')->nullable();
                }
                if (!Schema::hasColumn('renatio_formbuilder_forms', 'postback_field')) {
                    $table->string('postback_field')->nullable();
                }

                // Language and loading fields
                if (!Schema::hasColumn('renatio_formbuilder_forms', 'language')) {
                    $table->string('language')->default('nl');
                }
                if (!Schema::hasColumn('renatio_formbuilder_forms', 'loading_top')) {
                    $table->string('loading_top')->nullable();
                }
                if (!Schema::hasColumn('renatio_formbuilder_forms', 'loading_bottom')) {
                    $table->string('loading_bottom')->nullable();
                }

                // Prelander and special form types
                if (!Schema::hasColumn('renatio_formbuilder_forms', 'prelander')) {
                    $table->boolean('prelander')->default(false);
                }
                if (!Schema::hasColumn('renatio_formbuilder_forms', 'nat_form')) {
                    $table->boolean('nat_form')->default(false);
                }
                if (!Schema::hasColumn('renatio_formbuilder_forms', 'collect_energylabel')) {
                    $table->boolean('collect_energylabel')->default(false);
                }

                // Newsletter fields
                if (!Schema::hasColumn('renatio_formbuilder_forms', 'newsletter_signup')) {
                    $table->boolean('newsletter_signup')->default(false);
                }
                if (!Schema::hasColumn('renatio_formbuilder_forms', 'newsletter_signup_label')) {
                    $table->string('newsletter_signup_label')->nullable();
                }
                if (!Schema::hasColumn('renatio_formbuilder_forms', 'newsletter_default_checked')) {
                    $table->boolean('newsletter_default_checked')->default(false);
                }
                if (!Schema::hasColumn('renatio_formbuilder_forms', 'newsletter_signup_tooltip')) {
                    $table->text('newsletter_signup_tooltip')->nullable();
                }

                // Thank you message fields
                if (!Schema::hasColumn('renatio_formbuilder_forms', 'ty_message_source')) {
                    $table->string('ty_message_source')->default('current');
                }
                if (!Schema::hasColumn('renatio_formbuilder_forms', 'ty_message_code')) {
                    $table->string('ty_message_code')->nullable();
                }

                // Custom options
                if (!Schema::hasColumn('renatio_formbuilder_forms', 'custom_options')) {
                    $table->text('custom_options')->nullable();
                }

                // UTM and postback settings
                if (!Schema::hasColumn('renatio_formbuilder_forms', 'send_utm')) {
                    $table->boolean('send_utm')->default(false);
                }
                if (!Schema::hasColumn('renatio_formbuilder_forms', 'postback_key')) {
                    $table->string('postback_key')->nullable();
                }
            });
        }

        // Add fields to renatio_formbuilder_field_types table
        if (Schema::hasTable('renatio_formbuilder_field_types')) {
            Schema::table('renatio_formbuilder_field_types', function($table) {
                if (!Schema::hasColumn('renatio_formbuilder_field_types', 'is_default')) {
                    $table->boolean('is_default')->default(false);
                }
            });
        }

        // Add fields to backend_users table
        if (Schema::hasTable('backend_users')) {
            Schema::table('backend_users', function($table) {
                if (!Schema::hasColumn('backend_users', 'biography')) {
                    $table->string('biography', 511)->nullable();
                }
                if (!Schema::hasColumn('backend_users', 'social')) {
                    $table->text('social')->nullable();
                }
            });
        }
    }

    public function down()
    {
        // Remove fields from renatio_formbuilder_fields table
        if (Schema::hasTable('renatio_formbuilder_fields')) {
            Schema::table('renatio_formbuilder_fields', function($table) {
                $table->dropColumn([
                    'conditional_logic', 'cl_action', 'cl_field', 'cl_condition', 'cl_value',
                    'description', 'range', 'offer_example', 'required_field', 'hide_label',
                    'custom_validation'
                ]);
            });
        }

        // Remove fields from renatio_formbuilder_forms table
        if (Schema::hasTable('renatio_formbuilder_forms')) {
            Schema::table('renatio_formbuilder_forms', function($table) {
                $table->dropColumn([
                    'converse_pixels', 'utm_source', 'postback_field', 'language',
                    'loading_top', 'loading_bottom', 'prelander', 'nat_form', 'collect_energylabel',
                    'newsletter_signup', 'newsletter_signup_label', 'newsletter_default_checked',
                    'newsletter_signup_tooltip', 'ty_message_source', 'ty_message_code',
                    'custom_options', 'send_utm', 'postback_key'
                ]);
            });
        }

        // Remove fields from renatio_formbuilder_field_types table
        if (Schema::hasTable('renatio_formbuilder_field_types')) {
            Schema::table('renatio_formbuilder_field_types', function($table) {
                $table->dropColumn('is_default');
            });
        }

        // Remove fields from backend_users table
        if (Schema::hasTable('backend_users')) {
            Schema::table('backend_users', function($table) {
                $table->dropColumn(['biography', 'social']);
            });
        }
    }


}
