1.0.1:
    - Initialize plugin.
1.0.2: Add color field to blog categories
1.0.3:
    - Add description field to formbuilder fields
    - create_description_field_formbuilder.php
1.0.5:
    - Add range field to formbuilder fields
    - create_range_field_formbuilder.php
1.0.6:
    - Add offer_example field to formbuilder fields
    - create_offer_example_field_formbuilder.php
1.0.11:
    - Add thankyou page field
    - create_ty_field_formbuilder.php
1.0.12:
    - Add required field to formbuilder fields
    - create_required_field_formbuilder.php
1.0.13:
    - Add hide_label to formbuilder fields
    - create_hide_label_formbuilder.php
1.0.14:
    - Add converse_pixels to formbuilder form
    - create_pixels_field_formbuilder.php
1.0.15:
    - Add utm_source to formbuilder form
    - create_utm_field_formbuilder.php
1.0.16:
    - create_lang_field_formbuilder.php
1.0.17:
    - create_postback_field_formbuilder.php
1.0.18:
    - create_loading_field_formbuilder.php
1.0.19:
    - seed_prelander_radio_list.php
1.0.20:
    - create_prelander_field_formbuilder.php
1.0.21:
    - update_dob_alt_fieldtype.php
1.0.22:
    - seed_hypotheekrente_fields.php
1.0.23:
    - Update phonenumber formfield
    - update_telefoonnummer_fieldtype.php
1.0.24:
    - Update street city formfield
    - update_streetcity_fieldtype.php
1.0.25:
    - Seed alert fieldtype
    - seed_alert_fieldtype.php
1.0.26:
    - update alert fieldtype
    - update_alert_fieldtype.php
1.0.27:
    - update alert fieldtype 2
    - update_alert_fieldtype_2.php
1.0.28:
    - Update street city formfield 2
    - update_streetcity_fieldtype_2.php
1.0.29:
    - Update street city formfield 3
    - update_streetcity_fieldtype_2.php
1.0.30:
    - Update checkbox list fieldtype
    - update_checkboxlist.php
1.0.32:
    - Added conditional logic
    - create_cl_fields.php
1.0.33:
    - Update conditional logic fieldtypes
    - update_conlogic_fieldtypes.php
1.0.34:
    - Update radio fieldtype
    - update_radio_fieldtypes.php
1.1.1:
    - Create Nat Field
    - create_nat_field.php
1.1.2:
    - Update radio fieldtype
    - update_radio_fieldtypes.php
1.1.3:
    - Update raw fieldtype labels
    - update_raw_labels.php
1.1.4:
    - Update fields
    - update_fieldtype_labels.php
1.1.5:
    - Seed international phone fieldtype
    - seed_intphone_field.php
1.1.6:
    - Update prelander radio fieldtype
    - update_prelander_radio_fieldtype.php
1.1.7:
    - Update section fieldtype
    - update_section_fieldtype.php
1.1.8:
    - Seed firstname_lastname fieldtype
    - seed_firstname_lastname.php
1.1.9:
    - Seed Belgium Zipcode fieldtypes
    - seed_be_zipcode_fields.php
1.1.10:
    - Create collect_energylabel field
    - create_energylabel_field.php
1.1.11:
    - Seed Webmaster Role
    - seed_webmaster_role.php
1.1.12:
    - Update housenumber fieldtype
    - update_housenumber_fieldtype.php
1.1.13:
    - Seed verborgen veldtype
    - seed_hidden_fieldtype.php
1.1.14:
    - Update Slider bedrag veldtype
    - update_slider_bedrag.php
1.1.15:
    - Update Suggesties veldtypes
    - update_postcode_sug_fieldtype.php
1.1.16:
    - Update Suggesties veldtypes
    - update_slider_bedrag2.php
1.1.17:
    - Update Radio List veldtype
    - update_radiolist.php
1.1.18:
    - Update conditional logic fieldtypes
    - update_conditional_fieldtypes.php
1.1.19:
    - Update zipcode be fieldtype
    - update_zipcode_be_fieldtype.php
1.1.20:
    - Enable new zipcode be fieldtype
    - enable_zipcode_be_fieldtype.php
1.1.21:
    - Seed street dropdown
    - seed_street_dropdown.php
1.1.22:
    - Seed street dropdown
    - seed_street_dropdown_field.php
1.1.23:
    - Update zipcode validation
    - update_zipcode_fieldtype.php
1.1.24:
    - Update zipcode validation new
    - update_zipcode_be_fieldtype-2.php
1.1.25:
    - Update fieldtypes march 7
    - update_fieldtypes_3_7.php
1.1.26:
    - Update fieldtypes march 7 2
    - update_fieldtypes_3_7_2.php
1.1.27:
    - Default values for boolean form fields
    - update_form_customfields.php
1.1.28:
    - Delete backend user 2
    - delete_backenduser.php
1.1.29:
    - Fix backend user roles
    - set_role_backendusers.php
1.1.30:
    - Radiolist update
    - update_radiolist-2.php
1.1.31:
    - OTP veld
    - seed_otp_field.php
1.1.32:
    - OTP permissions
    - update_webmaster_role.php
1.1.33:
    - Create OTP veld
    - seed_otp_field2.php
1.1.34:
    - Update OTP veld
    - update_otp_field.php
1.1.35:
    - Update submit knop
    - update_submit_field.php
1.1.36:
    - Update OTP veld
    - update_otp_field2.php
1.1.37:
    - Update OTP veld
    - update_otp_field3.php
1.1.38:
    - Update OTP veld
    - update_otp_field4.php
1.1.40:
    - Seed Text-Number fieldtype
    - seed_text_number_fieldtype.php
1.2.0:
    - Disable page reload on cookiesettings
    - set_cookie_settings.php
1.2.1:
    - Change cookie text
    - set_cookie_settings_2.php
1.2.2:
    - Update default form settings
    - update_default_forms.php
1.2.3:
    - Add recipients to default forms
    - update_default_forms_2.php
1.2.4:
    - Update newsletter form
    - update_email_newsletter_fieldtype.php
1.2.5:
    - Create missing form fields
    - create_missing_fields.php
1.2.6:
    - Change form template for default forms
    - update_default_forms_3.php
1.2.7:
    - Create missing form fields 2
    - create_missing_fields_2.php
1.2.8:
    - Update alert fieldtype
    - update_alert_fieldtype_3.php
1.2.9:
    - Update webmaster role 2
    - update_webmaster_role_2.php
1.2.10:
    - Update webmaster role 3
    - update_webmaster_role_3.php
1.2.11:
    - Update zipcode-city field 3
    - update_zipcode_be_fieldtype3.php
1.2.12:
    - Update webmaster role 4
    - update_webmaster_role_4.php
1.2.13:
    - Disable browser translation of multi-option values
    - update_fieldtypes_disable_trans.php
1.2.14:
    - Update OTP veld
    - update_otp_field5.php
1.2.15:
    - Create thankyou connection
    - create_thankyou_connection.php
1.2.16:
    - Create name fieldtype variants
    - seed_inverted_names_fieldtypes.php
1.2.17:
    - Update checkbox list fieldtype
    - update_checkboxlist_2.php
1.2.18:
    - Update OTP veld
    - update_otp_field6.php
1.2.19:
    - Radiolist update
    - update_radiolist-3.php
1.2.20:
    - Radiolist update
    - update_radiolist-4.php
1.2.21:
    - Radiolist update
    - update_radiolist-5.php
1.2.22:
    - Added newsletter fields
    - create_newsletter_field.php
1.2.23:
    - Submit update
    - update_submit_field_2.php
1.2.24:
    - Submit update
    - update_submit_field_2.php
1.2.25:
    - Submit update
    - update_submit_field_2.php
1.2.26:
    - Added newsletter tooltip
    - create_newsletter_field_2.php
1.2.27:
    - Submit update
    - update_submit_field_2.php
1.2.28:
    - Email update
    - update_email_field.php
1.2.29:
    - Fieldtype updates
    - update_fieldtypes_19_12.php
1.2.30:
    - Added newsletter zipcode field
    - seed_newsletter_zip.php
1.2.33:
    - Newsletter update
    - update_newsletter_form.php
1.2.35:
    - Newsletter update
    - update_newsletter_form.php
1.2.36:
    - Newsletter checkbox field
    - create_newsletter_checkbox.php
1.2.37:
    - Update e-mail field
    - update_email_field.php
1.2.39:
    - Seed Admin Role
    - seed_admin_role.php
1.2.40:
    - Update Thomas Account
    - update_thomas_account.php
1.2.41:
    - Update e-mail field
    - update_email_field.php
1.2.42:
    - Update e-mail field
    - update_email_field.php
1.2.43:
    - Update nieuwsletter zipcode field
    - update_newsletter_zipcode_fieldtype.php
1.2.44:
    - Seed waarschuwing fieldtype
    - seed_waarschuwing_fieldtype.php
1.2.45:
    - Change cookie text
    - set_cookie_settings_3.php
1.2.46:
    - Update upload field
    - update_upload_field.php
1.2.47:
    - Update upload field
    - update_upload_field.php
1.2.48:
    - Create custom validation field
    - create_custom_validation.php
1.2.49:
    - Update naam fieldtype
    - update_naam_fieldtype.php
1.2.50:
    - Update site language fieldtypes
    - update_sitelang_fieldtypes.php
1.2.51:
    - Update telefoonnummer fieldtype
    - update_telefoonnummer_fieldtype.php
1.2.52:
    - Update date fieldtype
    - update_date_fieldtype.php
1.2.53:
    - Seed OTP veld 2
    - seed_otp_2_fieldtype.php
1.2.54:
    - Update otp fields for 4-digits
    - update_otp_fields.php
1.2.55:
    - Update otp field 2
    - update_otp_2_field.php
1.2.56:
    - Update otp field 2
    - update_otp_2_field.php
1.2.57:
    - Update otp field 2
    - update_otp_2_field.php
1.2.58:
    - Update checkboxlist
    - update_checkboxlist.php
1.2.59:
    - Update date fieltype
    - update_date_fieldtype.php
1.2.60:
    - Seed adresvinder fieldtype
    - seed_adresvinder_fieldtype.php
1.2.61:
    - Update newsletter email field
    - update_email_newsletter_fieldtype.php
    - update_newsletters_zip_fieldtype.php
1.2.62:
    - Update date fieltype
    - update_date_fieldtype.php
1.2.63:
    - Update adresvinder fieldtype
    - update_adresvinder_fieldtype.php
1.2.64:
    - Update adresvinder fieldtype
    - update_adresvinder_fieldtype.php
1.3.0:
    - Create custom options fields
    - create_custom_options_fields.php
1.3.1:
    - Create custom options fields
    - update_adresvinder_fieldtype.php
1.3.2:
    - Seed begin knop fieldtype
    - seed_begin_knop_fieldtype.php
1.3.3:
    - Update begin knop fieldtype
    - update_begin_knop.php
2.0.0:
    - Complete plugin consolidation with all previous updates
    - create_consolidated_schema.php
    - seed_all_fieldtypes.php
    - seed_all_roles.php
    - set_all_configurations.php
