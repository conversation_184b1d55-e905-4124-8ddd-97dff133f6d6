<?php namespace Vbo\Settings\Updates;

use October\Rain\Database\Updates\Seeder;
use <PERSON>V<PERSON>ce\SmallGDPR\Models\CookiesSettings;
use Renatio\FormBuilder\Models\Form;
use Renatio\FormBuilder\Models\FieldType;
use Config;

/**
 * Consolidated configuration seeder for VBO Settings Plugin v2.0
 * This seeder includes all configuration updates from versions 1.0.1 through 1.3.3
 */
class SetAllConfigurations extends Seeder
{
    public function run()
    {
        // Set cookie settings
        $this->setCookieSettings();
        
        // Update default forms
        $this->updateDefaultForms();
        
        // Update fieldtype configurations
        $this->updateFieldTypeConfigurations();
    }
    
    private function setCookieSettings()
    {
        // From set_cookie_settings.php
        CookiesSettings::set('cookies_bar_disable_page_reload', '1');
        
        // From set_cookie_settings_2.php and set_cookie_settings_3.php
        CookiesSettings::set('cookies_bar_text', 'Deze website gebruikt cookies om de gebruikerservaring te verbeteren. Door gebruik te maken van onze website ga je akkoord met alle cookies in overeenstemming met ons cookiebeleid.');
        CookiesSettings::set('cookies_bar_button_accept_text', 'Accepteren');
        CookiesSettings::set('cookies_bar_button_manage_text', 'Beheren');
        CookiesSettings::set('cookies_manage_title', 'Cookie instellingen');
        CookiesSettings::set('cookies_manage_text', 'Hier kun je je cookie voorkeuren instellen. Je kunt verschillende categorieën in- of uitschakelen. Voor meer informatie over cookies en welke gegevens we verzamelen, lees ons cookiebeleid.');
        CookiesSettings::set('cookies_manage_button_save', 'Opslaan');
        CookiesSettings::set('cookies_manage_button_accept_all', 'Alles accepteren');
    }
    
    private function updateDefaultForms()
    {
        $app_name = Config::get('app.name');
        
        // Update newsletter form
        $newsletter = Form::where('code', 'nieuwsbrief')->first();
        if ($newsletter) {
            $newsletter->from_email = "<EMAIL>";
            $newsletter->from_name = $app_name;
            $newsletter->template_code = "vbo.settings::mail.newsletter";
            $newsletter->save();
        }
        
        // Update partner form
        $partner = Form::where('code', 'contact-form-2')->first();
        if ($partner) {
            $partner->from_email = "<EMAIL>";
            $partner->from_name = $app_name;
            $partner->template_code = "vbo.settings::mail.partner";
            $partner->save();
        }
        
        // Update all forms to use proper template
        $forms = Form::whereNotIn('code', ['nieuwsbrief', 'contact-form-2'])->get();
        foreach ($forms as $form) {
            if (empty($form->template_code)) {
                $form->template_code = "renatio.formbuilder::mail.notification";
                $form->save();
            }
        }
    }
    
    private function updateFieldTypeConfigurations()
    {
        // Update specific fieldtypes with enhanced configurations
        $this->updatePhoneNumberFieldTypes();
        $this->updateAddressFieldTypes();
        $this->updateFormControlFieldTypes();
    }
    
    private function updatePhoneNumberFieldTypes()
    {
        // Update telefoonnummer fieldtype
        $phoneField = FieldType::where('code', 'text-6')->first();
        if ($phoneField) {
            $phoneField->name = 'Telefoonnummer (NL)';
            $phoneField->save();
        }
        
        // Update international phone fieldtype
        $intPhoneField = FieldType::where('code', 'telephone-int')->first();
        if ($intPhoneField) {
            $intPhoneField->name = 'Telefoonnummer (Internationaal)';
            $intPhoneField->save();
        }
    }
    
    private function updateAddressFieldTypes()
    {
        // Update street/city fieldtype
        $streetCityField = FieldType::where('code', 'straat-plaats')->first();
        if ($streetCityField) {
            $streetCityField->name = 'Straat + Plaats';
            $streetCityField->save();
        }
        
        // Update housenumber fieldtype
        $houseNumberField = FieldType::where('code', 'huisnummer')->first();
        if ($houseNumberField) {
            $houseNumberField->name = 'Huisnummer';
            $houseNumberField->save();
        }
    }
    
    private function updateFormControlFieldTypes()
    {
        // Update submit button fieldtype
        $submitField = FieldType::where('code', 'submit')->first();
        if ($submitField) {
            $submitField->name = 'Verzend knop';
            $submitField->save();
        }
        
        // Update section fieldtype
        $sectionField = FieldType::where('code', 'section')->first();
        if ($sectionField) {
            $sectionField->name = 'Sectie';
            $sectionField->save();
        }
        
        // Update upload fieldtype
        $uploadField = FieldType::where('code', 'upload')->first();
        if ($uploadField) {
            $uploadField->name = 'Bestand upload';
            $uploadField->save();
        }
    }
}
