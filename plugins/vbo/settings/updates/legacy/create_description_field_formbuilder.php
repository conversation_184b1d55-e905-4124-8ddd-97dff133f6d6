<?php namespace Vbo\Settings\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class createFormBuilderDescription extends Migration
{
    public function up()
    {
        Schema::table('renatio_formbuilder_fields', function($table)
        {
            $table->text('description', 65535)->nullable();
        });
    }

    public function down()
    {
        Schema::table('renatio_formbuilder_fields', function($table)
        {
            $table->dropColumn('description');
        });
    }
}
