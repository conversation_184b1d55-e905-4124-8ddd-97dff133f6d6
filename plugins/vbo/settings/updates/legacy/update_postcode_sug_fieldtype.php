<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class UpdateZipcodeSugFieldType extends Seeder
{
  public function run()
  {
    $field = FieldType::where('code', 'text-3-4')->first();
    $street = FieldType::where('code', 'text-4-3')->first();
    $city = FieldType::where('code', 'text-4-2-2')->first();

    if ( $field ) {
      $field->markup = File::get( __DIR__.'/fields/'.'_postcode_sug.htm' );

      $field->save();
    }
    if ( $street ) {
      $street->markup = File::get( __DIR__.'/fields/'.'_straat_sug.htm' );

      $street->save();
    }
    if ( $city ) {
      $city->markup = File::get( __DIR__.'/fields/'.'_plaats_sug.htm' );

      $city->save();
    }
  }
}
