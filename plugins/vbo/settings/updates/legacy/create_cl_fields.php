<?php namespace Vbo\Settings\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class CreateClFields extends Migration
{
    public function up()
    {
        Schema::table('renatio_formbuilder_fields', function($table)
        {
            $table->boolean('conditional_logic');
            $table->string('cl_action')->nullable();
            $table->string('cl_field')->nullable();
            $table->string('cl_condition')->nullable();
            $table->string('cl_value')->nullable();
        });
    }

    public function down()
    {
        Schema::table('renatio_formbuilder_fields', function($table)
        {
            $table->dropColumn('conditional_logic');
            $table->dropColumn('cl_action');
            $table->dropColumn('cl_field');
            $table->dropColumn('cl_condition');
            $table->dropColumn('cl_value');
        });
    }
}
