<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class UpdateOtpField3 extends Seeder
{
  public function run()
  {
    $field = FieldType::where('code', 'telefoonnummer-otp')->first();

    if ( $field ) {
        $field->markup = File::get( __DIR__.'/fields/'.'_telefoonnummer_otp.htm' );

        $field->save();
    }
  }
}
