<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class SeedIntPhoneFieldType extends Seeder
{
  public function run()
  {
    $path = __DIR__.'/fields/';
    $fieldtypes = FieldType::all();
    $seeding = $fieldtypes->where('code', 'telefoonnummer-int')->first();

    if ( !$seeding ) {
      FieldType::create([
        'name' => 'Telefoonnummer INT',
        'code' => 'telefoonnummer-int',
        'markup' => File::get($path.'_telephone_int.htm'),
        'description' => 'Internationaal telefoonnummer'
      ]);
    }
  }
}
