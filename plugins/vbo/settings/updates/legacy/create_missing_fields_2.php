<?php namespace Vbo\Settings\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class CreateMissingFormFields2 extends Migration
{
    public function up()
    {
        Schema::table('renatio_formbuilder_forms', function($table)
        {
            if ( !Schema::hasColumn('renatio_formbuilder_forms', 'markup')) {
                $table->text('markup')->nullable();
            }
        });
    }

    public function down()
    {
        Schema::table('renatio_formbuilder_forms', function($table)
        {
            if ( Schema::hasColumn('renatio_formbuilder_forms', 'markup')) {
                $table->dropColumn('markup');
            }
        });
    }
}
