<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class SeedFieldTypesHypotheekrente extends Seeder
{
  public function run()
  {
    $path = __DIR__.'/fields/';
    $fieldtypes = FieldType::all();
    $seedingVerhouding = $fieldtypes->where('code', 'hypotheekrente-verhouding')->first();
    $seedingPeriode = $fieldtypes->where('code', 'hypotheekrente-periode')->first();

    if ( !$seedingVerhouding ) {
      FieldType::create([
        'name' => 'Hypotheekrente schuld-marktwaarde verhouding',
        'code' => 'hypotheekrente-verhouding',
        'description' => 'Actieve schuld-marktwaarde verhouding van de gebruiker',
        'markup' => File::get($path.'_hypotheekrente_verhouding.htm'),
      ]);
    }
    if ( !$seedingPeriode ) {
        FieldType::create([
          'name' => 'Hypotheekrente rentevaste periode',
          'code' => 'hypotheekrente-periode',
          'description' => 'Actieve rentevaste periode van de gebruiker',
          'markup' => File::get($path.'_hypotheekrente_periode.htm'),
        ]);
      }
  }
}
