<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class SeedAdresVinderFieldtype extends Seeder
{
  public function run()
  {
    $path = __DIR__.'/fields/';
    $fieldtypes = FieldType::all();
    $seeding = $fieldtypes->where('code', 'adresvinder')->first();

    if ( !$seeding ) {
      FieldType::create([
        'name' => 'Adresvinder',
        'code' => 'adresvinder',
        'markup' => File::get($path.'_adresvinder.htm'),
        'description' => 'Toont een adres veld met dropdown om een woonadres te vinden'
      ]);
    }
  }
}
