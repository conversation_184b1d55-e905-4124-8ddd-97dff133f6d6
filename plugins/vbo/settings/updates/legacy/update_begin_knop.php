<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class UpdateBeginKnop extends Seeder
{
  public function run()
  {
    $field = FieldType::where('code', 'submit-2-2-2')->first();

    if ( $field ) {
        $field->markup = File::get( __DIR__.'/fields/'.'_begin_knop.htm' );

        $field->save();
    }
  }
}
