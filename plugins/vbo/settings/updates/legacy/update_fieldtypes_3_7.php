<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class UpdateFieldTypesMarch7 extends Seeder
{
  public function run()
  {
    $zipcodeCity = FieldType::where('code', 'postcode-woonplaats')->first();
    $streetDropdown = FieldType::where('code', 'straatnaam-dropdown')->first();
    $streetSugg = FieldType::where('code', 'text-4-3')->first();
    $housenumberSugg = FieldType::where('code', 'text-3-2-2')->first();
    $citySugg = FieldType::where('code', 'text-4-2-2')->first();
    $zipcodeSugg = FieldType::where('code', 'text-3-4')->first();

    if ( $zipcodeCity ) {
      $zipcodeCity->markup = File::get( __DIR__.'/fields/'.'_zipcode_city.htm' );

      $zipcodeCity->save();
    }

    if ( $streetDropdown ) {
      $streetDropdown->markup = File::get( __DIR__.'/fields/'.'_straatnaam_dropdown.htm' );

      $streetDropdown->save();
    }

    if ( $streetSugg ) {
      $streetSugg->markup = File::get( __DIR__.'/fields/'.'_straatnaam_suggesties.htm' );

      $streetSugg->save();
    }

    if ( $housenumberSugg ) {
      $housenumberSugg->markup = File::get( __DIR__.'/fields/'.'_housenumber_sug.htm' );

      $housenumberSugg->save();
    }

    if ( $citySugg ) {
      $citySugg->markup = File::get( __DIR__.'/fields/'.'_woonplaats_suggesties.htm' );

      $citySugg->save();
    }

    if ( $zipcodeSugg ) {
      $zipcodeSugg->markup = File::get( __DIR__.'/fields/'.'_postcode_sug.htm' );

      $zipcodeSugg->save();
    }

  }
}
