<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class SeedStreetDropdown extends Seeder
{
  public function run()
  {
    $housenumber = FieldType::where('code', 'text-3-2')->first();
    $housenumberSuggestions = FieldType::where('code', 'text-3-2-2')->first();

    if ( $housenumber ) {
      $housenumber->markup = File::get( __DIR__.'/fields/'.'_housenumber.htm' );

      $housenumber->save();
  }
  if ( $housenumberSuggestions ) {
    $housenumberSuggestions->markup = File::get( __DIR__.'/fields/'.'_housenumber.htm' );

    $housenumberSuggestions->save();
}
  }
}
