<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class UpdateSliderBedrag extends Seeder
{
  public function run()
  {
    $field = FieldType::where('code', 'slider')->first();
    $sliderBedrag = FieldType::where('code', 'slider-2')->first();

    if ( $field ) {
        $field->markup = File::get( __DIR__.'/fields/'.'_slider_bedrag.htm' );

        $field->save();
    }
    if ( $sliderBedrag ) {
      $sliderBedrag->markup = File::get( __DIR__.'/fields/'.'_slider_nummer.htm' );

      $sliderBedrag->save();
  }
  }
}
