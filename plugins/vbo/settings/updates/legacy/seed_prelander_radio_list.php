<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class SeedFieldTypes extends Seeder
{
  public function run()
  {
    $path = __DIR__.'/fields/';
    $fieldtypes = FieldType::all();
    $seeding = $fieldtypes->where('code', 'prelander-radio-lijst')->first();
    
    if ( !$seeding ) {
      FieldType::create([
        'name' => 'Prelander Radio lijst',
        'code' => 'prelander-radio-lijst',
        'markup' => File::get($path.'_prelander_radio.htm'),
      ]);
    }
  }
}
