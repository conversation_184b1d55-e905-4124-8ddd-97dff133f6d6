<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class SeedStreetDropdownField extends Seeder
{
  public function run()
  {
    $streetDropdown = FieldType::where('code', 'straatnaam-dropdown')->first();
    $path = __DIR__.'/fields/';

    if ( !$streetDropdown ) {
      FieldType::create([
        'name' => 'Straatnaam dropdown',
        'code' => 'straatnaam-dropdown',
        'markup' => File::get($path.'_straatnaam_dropdown.htm'),
      ]);
    }

  }
}
