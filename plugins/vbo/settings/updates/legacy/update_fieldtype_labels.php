<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class UpdateFieldTypeLabels extends Seeder
{
  public function run()
  {
    $slider = FieldType::where('code', 'slider')->first();
    $slidergetal = FieldType::where('code', 'slider-2')->first();
    $radiolist = FieldType::where('code', 'radio_list')->first();

    if ( $slider ) {
        $slider->markup = File::get( __DIR__.'/fields/'.'_slider_bedrag.htm' );
        $slider->save();
    }
    if ( $slidergetal ) {
        $slidergetal->markup = File::get( __DIR__.'/fields/'.'_slider_nummer.htm' );
        $slidergetal->save();
    }
    if ( $radiolist ) {
        $radiolist->markup = File::get( __DIR__.'/fields/'.'_radio_list.htm' );
        $radiolist->save();
    }
  }
}
