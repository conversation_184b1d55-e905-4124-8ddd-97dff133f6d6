<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class UpdateConLogicFields extends Seeder
{
  public function run()
  {
    $checkbox = FieldType::where('code', 'checkbox')->first();
    $checkboxList = FieldType::where('code', 'checkbox_list')->first();
    $radioList = FieldType::where('code', 'radio_list')->first();

    if ( $checkbox ) {
        $checkbox->markup = File::get( __DIR__.'/fields/'.'_checkbox.htm' );
        $checkbox->save();
    }
    if ( $checkboxList ) {
        $checkboxList->markup = File::get( __DIR__.'/fields/'.'_checkbox_list.htm' );
        $checkboxList->save();
    }
    if ( $radioList ) {
        $radioList->markup = File::get( __DIR__.'/fields/'.'_radio_list.htm' );
        $radioList->save();
    }
  }
}
