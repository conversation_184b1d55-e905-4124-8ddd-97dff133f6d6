<?php

namespace Vbo\Settings\Updates;

use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\Form;

class UpdateDefaultForms2 extends Seeder
{
  public function run()
  {
    $newsletter = Form::where('code', 'nieuwsbrief')->first();
    $partner = Form::where('code', 'contact-form-2')->first();
    $recipients = [
        [
            'email' => '<EMAIL>',
            'recipient_name' => '<PERSON>',
        ],
    ];

    if ( $newsletter ) {
        $newsletter->recipients = $recipients;

        $newsletter->save();
    }
    if ( $partner ) {
        $partner->recipients = $recipients;

        $partner->save();
    }
  }
}
