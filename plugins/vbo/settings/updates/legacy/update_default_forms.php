<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\Form;
use Config;

class UpdateDefaultForms extends Seeder
{
  public function run()
  {
    $newsletter = Form::where('code', 'nieuwsbrief')->first();
    $partner = Form::where('code', 'contact-form-2')->first();
    $app_name = Config::get('app.name');

    if ( $newsletter ) {
        $newsletter->from_email = "<EMAIL>";
        $newsletter->from_name = $app_name;
        // $newsletter->template_code = "vbo.settings::mail.newsletter";

        $newsletter->save();
    }
    if ( $partner ) {
        $partner->from_email = "<EMAIL>";
        $partner->from_name = $app_name;
        // $partner->template_code = "vbo.settings::mail.partner";

        $partner->save();
    }
  }
}
