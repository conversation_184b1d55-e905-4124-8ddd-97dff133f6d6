<?php

namespace Vbo\Settings\Updates;

use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\Form;

class UpdateDefaultForms3 extends Seeder
{
  public function run()
  {
    $newsletter = Form::where('code', 'nieuwsbrief')->first();
    $partner = Form::where('code', 'contact-form-2')->first();

    if ( $newsletter ) {
        $newsletter->template_code = "vbo.settings::mail.newsletter";
        $newsletter->save();
    }
    if ( $partner ) {
        $partner->template_code = "vbo.settings::mail.partner";
        $partner->save();
    }
  }
}
