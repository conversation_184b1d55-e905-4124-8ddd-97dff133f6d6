<?php namespace Vbo\Settings\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class CreateNewsletterCheckboxField extends Migration
{
    public function up()
    {
        Schema::table('renatio_formbuilder_forms', function($table)
        {
            $table->boolean('newsletter_default_checked')->default(false)->after('newsletter_signup');
        });
    }

    public function down()
    {
        Schema::table('renatio_formbuilder_forms', function($table)
        {
            $table->dropColumn('newsletter_default_checked');
        });
    }
}
