<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class SeedHiddenFieldType extends Seeder
{
  public function run()
  {
    $path = __DIR__.'/fields/';
    $fieldtypes = FieldType::all();
    $seeding = $fieldtypes->where('code', 'hidden')->first();

    if ( !$seeding ) {
      FieldType::create([
        'name' => 'Verborgen veld',
        'code' => 'hidden',
        'markup' => File::get($path.'_hidden.htm'),
      ]);
    }
  }
}
