<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class SeedTextNumberFieldtype extends Seeder
{
  public function run()
  {
    $path = __DIR__.'/fields/';
    $fieldtypes = FieldType::all();
    $seeding = $fieldtypes->where('code', 'text-number')->first();

    if ( !$seeding ) {
      FieldType::create([
        'name' => 'Tekst (nummer)',
        'code' => 'text-number',
        'markup' => File::get($path.'_text_number.htm'),
        'description' => 'Tekstveld voor nummers'
      ]);
    }
  }
}
