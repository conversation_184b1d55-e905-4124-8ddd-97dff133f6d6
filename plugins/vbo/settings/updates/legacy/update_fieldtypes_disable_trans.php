<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class UpdateFieldTypeDisableTranslation extends Seeder
{
    public function run()
    {
        $dropdown = FieldType::where('code', 'dropdown')->first();
        $dropdown_small = FieldType::where('code', 'dropdown-2')->first();
        $checkbox_list = FieldType::where('code', 'checkbox_list')->first();
        $radio_list = FieldType::where('code', 'radio_list')->first();


        if ( $dropdown ) {
            $dropdown->markup = File::get( __DIR__.'/fields/'.'_dropdown.htm' );
            $dropdown->save();
        }
        if ( $dropdown_small ) {
            $dropdown_small->markup = File::get( __DIR__.'/fields/'.'_dropdown_small.htm' );
            $dropdown_small->save();
        }
        if ( $checkbox_list ) {
            $checkbox_list->markup = File::get( __DIR__.'/fields/'.'_checkbox_list.htm' );
            $checkbox_list->save();
        }
        if ( $radio_list ) {
            $radio_list->markup = File::get( __DIR__.'/fields/'.'_radio_list.htm' );
            $radio_list->save();
        }

    }
}
