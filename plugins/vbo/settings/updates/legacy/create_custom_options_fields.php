<?php namespace Vbo\Settings\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class CreateCustomOptionsFields extends Migration
{
    public function up()
    {
        Schema::table('renatio_formbuilder_fields', function($table)
        {
            $table->text('custom_options')->nullable();
        });
        Schema::table('renatio_formbuilder_forms', function($table)
        {
            $table->text('custom_options')->nullable();
        });
    }

    public function down()
    {
        Schema::table('renatio_formbuilder_fields', function($table)
        {
            $table->dropColumn('custom_options');
        });
        Schema::table('renatio_formbuilder_forms', function($table)
        {
            $table->dropColumn('custom_options');
        });
    }
}
