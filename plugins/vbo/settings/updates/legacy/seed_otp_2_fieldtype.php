<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class SeedOtp2Fieldtype extends Seeder
{
    public function run()
    {
        $path = __DIR__.'/fields/';
        $fieldtypes = FieldType::all();
        $seeding = $fieldtypes->where('code', 'telefoonnummer-otp-2')->first();

        if ( !$seeding ) {
            FieldType::create([
                'name' => 'Telefoonnummer + OTP (vinkje)',
                'code' => 'telefoonnummer-otp-2',
                'markup' => File::get($path.'_telefoonnummer_otp_2.htm'),
                'description' => 'Telefoonnummer met OTP vinkje'
            ]);
        }
    }
}
