<?php namespace Vbo\Settings\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class CreateNewsletterField2 extends Migration
{
    public function up()
    {
        Schema::table('renatio_formbuilder_forms', function($table)
        {
            $table->string('newsletter_signup_tooltip')->nullable()->after('newsletter_signup_label');
        });
    }

    public function down()
    {
        Schema::table('renatio_formbuilder_forms', function($table)
        {
            $table->dropColumn('newsletter_signup_tooltip');
        });
    }
}
