<?php namespace Vbo\Settings\Updates;

use Seeder;
use Backend\Models\UserRole;

class SeedWebmasterRole2 extends Seeder
{
    public function run()
    {
        $checkRole = UserRole::where('code', '=', 'webmaster')->first();

        if ( $checkRole === null ) {
            $role = UserRole::create([
                'name' => 'Website beheerder',
                'code' => 'webmaster',
                'description' => 'Beheerders rol voor webmasters',
                'permissions' => [
                    "general.view_offline" => "1",
                    "general.backend" => "1",
                    "general.backend.view_offline" => "1",
                    "dashboard" => "1",
                    "rainlab.pages.manage_pages" => "1",
                    "rainlab.pages.manage_menus" => "1",
                    "rainlab.pages.manage_content" => "1",
                    "mail.templates" => "1",
                    "mail.settings" => "1",
                    "cms.themes" => "1",
                    "cms.theme_customize" => "1",
                    "cms.maintenance_mode" => "1",
                    "media.library" => "1",
                    "media.library.create" => "1",
                    "media.library.delete" => "1",
                    "utilities.logs" => "1",
                    "settings.manage_sites" => "1",
                    "preferences" => "1",
                    "tailor.entry.cfb7c110e5f0478996d28a104d6a2730" => "1",
                    "tailor.entry.cfb7c110e5f0478996d28a104d6a2730.create" => "1",
                    "tailor.entry.cfb7c110e5f0478996d28a104d6a2730.publish" => "1",
                    "tailor.entry.cfb7c110e5f0478996d28a104d6a2730.delete" => "1",
                    "tailor.entry.61990c1bd2e74105888eb7f2fdf9a6b7" => "1",
                    "tailor.entry.61990c1bd2e74105888eb7f2fdf9a6b7.create" => "1",
                    "tailor.entry.61990c1bd2e74105888eb7f2fdf9a6b7.publish" => "1",
                    "tailor.entry.61990c1bd2e74105888eb7f2fdf9a6b7.delete" => "1",
                    "tailor.entry.1453bd68f2cc422ea4eec6dcca3dbbe0" => "1",
                    "tailor.entry.1453bd68f2cc422ea4eec6dcca3dbbe0.create" => "1",
                    "tailor.entry.1453bd68f2cc422ea4eec6dcca3dbbe0.publish" => "1",
                    "tailor.entry.1453bd68f2cc422ea4eec6dcca3dbbe0.delete" => "1",
                    "tailor.global.42e23050f77d4862952067db9ad209b6" => "1",
                    "albrightlabs.redirects.manage_redirects" => "1",
                    "janvince.smallgdpr.access_cookies_settings" => "1",
                    "netsetters.klantenvertellen.access_settings" => "1",
                    "rainlab.location.access_settings" => "1",
                    "rainlab.sitemap.access_definitions" => "1",
                    "rainlab.translate.manage_messages" => "1",
                    "renatio.formbuilder.access_forms" => "1",
                    "renatio.formbuilder.access_forms.create" => "1",
                    "renatio.formbuilder.access_forms.update" => "1",
                    "renatio.formbuilder.access_forms.delete" => "1",
                    "renatio.formbuilder.access_forms.import_export" => "1",
                    "renatio.formbuilder.access_form_logs" => "1",
                    "renatio.formbuilder.access_form_logs.preview" => "1",
                    "renatio.formbuilder.access_form_logs.truncate" => "1",
                    "renatio.formbuilder.access_form_logs.delete" => "1",
                    "renatio.formbuilder.access_form_logs.export" => "1",
                    "renatio.formbuilder.access_settings" => "1",
                    "manage_plugin" => "1",
                    "offline.boxes.manage_settings" => "1",
                    "offline.boxes.access_editor" => "1"
                ]
            ]);
        }
    }
}
