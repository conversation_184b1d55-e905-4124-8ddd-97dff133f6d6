<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class UpdateRawLabels extends Seeder
{
  public function run()
  {
    $text = FieldType::where('code', 'text')->first();
    $slider = FieldType::where('code', 'slider')->first();

    if ( $text ) {
        $text->markup = File::get( __DIR__.'/fields/'.'_text.htm' );
        $text->save();
    }
    if ( $slider ) {
        $slider->markup = File::get( __DIR__.'/fields/'.'_slider_bedrag.htm' );
        $slider->save();
    }
  }
}
