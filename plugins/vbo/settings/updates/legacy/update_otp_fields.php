<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class UpdateOtpFields extends Seeder
{
  public function run()
  {
    $field = FieldType::where('code', 'telefoonnummer-otp')->first();
    $field2 = FieldType::where('code', 'telefoonnummer-otp-2')->first();

    if ( $field ) {
        $field->markup = File::get( __DIR__.'/fields/'.'_telefoonnummer_otp.htm' );

        $field->save();
    }
    if ( $field2 ) {
        $field2->markup = File::get( __DIR__.'/fields/'.'_telefoonnummer_otp_2.htm' );

        $field2->save();
    }
  }
}
