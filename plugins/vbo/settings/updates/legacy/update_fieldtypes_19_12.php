<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class UpdateFieldTypes19Dec extends Seeder
{
    public function run()
    {
        $dob = FieldType::where('code', 'text-7')->first();
        $dob_alt = FieldType::where('code', 'text-7-3')->first();
        $municipality = FieldType::where('code', 'gemeente')->first();
        $textarea = FieldType::where('code', 'textarea')->first();
        $woonplaats = FieldType::where('code', 'text-4-2')->first();

        if ( $dob ) {
            $dob->markup = File::get( __DIR__.'/fields/'.'_geboortedatum.htm' );
            $dob->save();
        }
        if ( $dob_alt ) {
            $dob_alt->markup = File::get( __DIR__.'/fields/'.'_dob_alt.htm' );
            $dob_alt->save();
        }
        if ( $municipality ) {
            $municipality->markup = File::get( __DIR__.'/fields/'.'_gemeente.htm' );
            $municipality->save();
        }
        if ( $textarea ) {
            $textarea->markup = File::get( __DIR__.'/fields/'.'_textarea.htm' );
            $textarea->save();
        }
        if ( $woonplaats ) {
            $woonplaats->markup = File::get( __DIR__.'/fields/'.'_woonplaats.htm' );
            $woonplaats->save();
        }

    }
}
