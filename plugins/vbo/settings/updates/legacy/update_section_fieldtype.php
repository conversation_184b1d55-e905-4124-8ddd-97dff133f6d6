<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class UpdateSectionFieldType extends Seeder
{
  public function run()
  {
    $section = FieldType::where('code', 'secrtion')->first();

    if ( $section ) {
        $section->markup = File::get( __DIR__.'/fields/'.'_section.htm' );
        $section->save();
    }
  }
}
