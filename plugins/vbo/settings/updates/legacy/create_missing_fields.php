<?php namespace Vbo\Settings\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class CreateMissingFormFields extends Migration
{
    public function up()
    {
        Schema::table('renatio_formbuilder_forms', function($table)
        {
            if ( !Schema::hasColumn('renatio_formbuilder_forms', 'template_code')) {
                $table->string('template_code')->nullable();
            }
            if ( !Schema::hasColumn('renatio_formbuilder_forms', 'response_template_code')) {
                $table->string('response_template_code')->nullable();
            }
            if ( !Schema::hasColumn('renatio_formbuilder_forms', 'has_floating_labels')) {
                $table->boolean('has_floating_labels')->default(false);
            }
            if ( !Schema::hasColumn('renatio_formbuilder_forms', 'is_log_enabled')) {
                $table->boolean('is_log_enabled')->default(true);
            }
            if ( !Schema::hasColumn('renatio_formbuilder_forms', 'is_autoresponder_log_enabled')) {
                $table->boolean('is_autoresponder_log_enabled')->default(true);
            }
        });
    }

    public function down()
    {
        Schema::table('renatio_formbuilder_forms', function($table)
        {
            if ( Schema::hasColumn('renatio_formbuilder_forms', 'template_code')) {
                $table->dropColumn('template_code');
            }
            if ( Schema::hasColumn('renatio_formbuilder_forms', 'response_template_code')) {
                $table->dropColumn('response_template_code');
            }
            if ( Schema::hasColumn('renatio_formbuilder_forms', 'has_floating_labels')) {
                $table->dropColumn('has_floating_labels');
            }
            if ( Schema::hasColumn('renatio_formbuilder_forms', 'is_log_enabled')) {
                $table->dropColumn('is_log_enabled');
            }
            if ( Schema::hasColumn('renatio_formbuilder_forms', 'is_autoresponder_log_enabled')) {
                $table->dropColumn('is_autoresponder_log_enabled');
            }
        });
    }
}
