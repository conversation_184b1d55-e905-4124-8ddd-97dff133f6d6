<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class UpdateFieldTypesMarch72 extends Seeder
{
  public function run()
  {
    $zipcode = FieldType::where('code', 'text-3')->first();
    $zipcodeSugg = FieldType::where('code', 'text-3-4')->first();

    if ( $zipcode ) {
      $zipcode->markup = File::get( __DIR__.'/fields/'.'_postcode.htm' );

      $zipcode->save();
    }

    if ( $zipcodeSugg ) {
      $zipcodeSugg->markup = File::get( __DIR__.'/fields/'.'_postcode_sug.htm' );

      $zipcodeSugg->save();
    }

  }
}
