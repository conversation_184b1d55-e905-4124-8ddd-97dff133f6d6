<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class UpdateNewsletterZipcodeFieldtype extends Seeder
{
  public function run()
  {
    $field = FieldType::where('code', 'postcode-nieuwsbrief')->first();

    if ( $field ) {
        $field->markup = File::get( __DIR__.'/fields/'.'_nieuwsbrief_postcode.htm' );

        $field->save();
    }
  }
}
