<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class SeedBeZipcode extends Seeder
{
  public function run()
  {
    $path = __DIR__.'/fields/';
    $fieldtypes = FieldType::all();
    $zipcodeCity = $fieldtypes->where('code', 'postcode-woonplaats')->first();
    $municipalityField = $fieldtypes->where('code', 'gemeente')->first();

    if ( !$zipcodeCity ) {
      FieldType::create([
        'name' => 'Postcode / Woonplaats',
        'code' => 'postcode-woonplaats',
        'markup' => File::get($path.'_zipcode_city.htm'),
        'description' => 'Toont dropdown met postcode en gemeente'
      ]);
    }
    if ( !$municipalityField ) {
        FieldType::create([
          'name' => 'Gemeente',
          'code' => 'gemeente',
          'markup' => File::get($path.'_gemeente.htm'),
          'description' => 'Gemeente veld voor Belgie'
        ]);
      }
  }
}
