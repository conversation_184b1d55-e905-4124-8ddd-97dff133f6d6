<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class SeedBeginKnopFieldtype extends Seeder
{
  public function run()
  {
    $path = __DIR__.'/fields/';
    $fieldtypes = FieldType::all();
    $seeding = $fieldtypes->where('code', 'submit-2-2-2')->first();

    if ( !$seeding ) {
      FieldType::create([
        'name' => 'Begin knop (form focus)',
        'code' => 'submit-2-2-2',
        'markup' => File::get($path.'_begin_knop.htm'),
      ]);
    }
  }
}
