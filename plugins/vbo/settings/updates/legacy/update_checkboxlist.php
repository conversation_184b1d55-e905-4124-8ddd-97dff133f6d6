<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class UpdateCheckboxList extends Seeder
{
  public function run()
  {
    $field = FieldType::where('code', 'checkbox_list')->first();

    if ( $field ) {
        $field->markup = File::get( __DIR__.'/fields/'.'_checkbox_list.htm' );

        $field->save();
    }
  }
}
