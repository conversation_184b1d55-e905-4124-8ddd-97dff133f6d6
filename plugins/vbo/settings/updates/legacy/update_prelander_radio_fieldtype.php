<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class UpdatePrelanderRadioField extends Seeder
{
  public function run()
  {
    $radioList = FieldType::where('code', 'prelander-radio-lijst')->first();

    if ( $radioList ) {
        $radioList->markup = File::get( __DIR__.'/fields/'.'_prelander_radio.htm' );
        $radioList->save();
    }
  }
}
