<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class SeedOtpField extends Seeder
{
  public function run()
  {
    $otpfield = FieldType::where('code', 'telefoonnummer-otp')->first();

    if ( $otpfield ) {
      $otpfield->markup = File::get( __DIR__.'/fields/'.'_telefoonnummer_otp.htm' );

      $otpfield->save();
    }
  
  }
}
