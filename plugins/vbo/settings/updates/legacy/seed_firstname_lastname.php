<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class SeedFirstNameLastName extends Seeder
{
  public function run()
  {
    $path = __DIR__.'/fields/';
    $fieldtypes = FieldType::all();
    $seeding = $fieldtypes->where('code', 'firstname_lastname')->first();

    if ( !$seeding ) {
      FieldType::create([
        'name' => 'Naam (met voornaam)',
        'code' => 'firstname_lastname',
        'markup' => File::get($path.'_firstname_lastname.htm'),
        'description' => 'Toont het naam veld met voornaam'
      ]);
    }
  }
}
