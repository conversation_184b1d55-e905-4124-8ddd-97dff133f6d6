<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class SeedWaarschuwingFieldtype extends Seeder
{
  public function run()
  {
    $path = __DIR__.'/fields/';
    $fieldtypes = FieldType::all();
    $seeding = $fieldtypes->where('code', 'waarschuwing')->first();

    if ( !$seeding ) {
      FieldType::create([
        'name' => 'Waarschuwing',
        'code' => 'waarschuwing',
        'markup' => File::get($path.'_waarschuwing.htm'),
      ]);
    }
  }
}
