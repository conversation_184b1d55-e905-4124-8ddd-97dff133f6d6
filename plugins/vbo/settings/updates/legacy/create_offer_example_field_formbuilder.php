<?php namespace Vbo\Settings\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class createFormBuilderOfferItem extends Migration
{
    public function up()
    {
        Schema::table('renatio_formbuilder_fields', function($table)
        {
            $table->tinyInteger('offer_item')->default(0);
        });
    }

    public function down()
    {
        Schema::table('renatio_formbuilder_fields', function($table)
        {
            $table->dropColumn('offer_item');
        });
    }
}
