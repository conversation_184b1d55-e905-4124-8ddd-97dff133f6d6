<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class UpdateZipcodeBeFieldtype extends Seeder
{
  public function run()
  {
    $field = FieldType::where('code', 'postcode-woonplaats')->first();

    if ( $field ) {
        $field->markup = File::get( __DIR__.'/fields/'.'_zipcode_city.htm' );

        $field->save();
    }
  }
}
