<?php namespace Vbo\Settings\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class createFormBuilderRangeField extends Migration
{
    public function up()
    {
        Schema::table('renatio_formbuilder_fields', function($table)
        {
            $table->integer('min')->nullable()->default(0);
            $table->integer('max')->nullable()->default(0);
            $table->integer('steps')->nullable()->default(0);
        });
    }

    public function down()
    {
        Schema::table('renatio_formbuilder_fields', function($table)
        {
            $table->dropColumn('min');
            $table->dropColumn('max');
            $table->dropColumn('steps');
        });
    }
}
