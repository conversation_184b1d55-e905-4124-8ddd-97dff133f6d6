<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class UpdateRadioListFieldType2 extends Seeder
{
  public function run()
  {
    $field = FieldType::where('code', 'radio_list')->first();

    if ( $field ) {
        $field->markup = File::get( __DIR__.'/fields/'.'_radio_list.htm' );

        $field->save();
    }
  }
}
