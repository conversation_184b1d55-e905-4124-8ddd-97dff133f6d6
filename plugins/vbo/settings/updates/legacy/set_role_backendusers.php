<?php namespace Vbo\Settings\Updates;

use Seeder;
use Backend\Models\User;

class SetRoleBackendUsers extends Seeder
{
    public function run()
    {
        $thomas = User::find(4);
        $marco = User::find(5);
        $jorrit = User::find(6);
        $mike = User::find(7);
        $maurits = User::find(8);
        if ( $thomas ) {
            $thomas->role = 3;
            $thomas->save();
        }
        if ( $marco ) {
            $marco->role = 3;
            $marco->save();
        }
        if ( $jorrit ) {
            $jorrit->role = 3;
            $jorrit->save();
        }
        if ( $mike ) {
            $mike->role = 3;
            $mike->save();
        }
        if ( $maurits ) {
            $maurits->role = 3;
            $maurits->save();
        }
    }
}
