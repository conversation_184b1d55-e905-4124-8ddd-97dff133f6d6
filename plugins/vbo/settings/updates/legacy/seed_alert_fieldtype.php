<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class SeedAlertFieldType extends Seeder
{
  public function run()
  {
    $path = __DIR__.'/fields/';
    $fieldtypes = FieldType::all();
    $seeding = $fieldtypes->where('code', 'melding-blok')->first();

    if ( !$seeding ) {
      FieldType::create([
        'name' => 'Melding blok',
        'code' => 'melding-blok',
        'markup' => File::get($path.'_alert.htm'),
      ]);
    }
  }
}
