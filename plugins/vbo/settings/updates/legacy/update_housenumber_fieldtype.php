<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class UpdateHouseNumberFieldType extends Seeder
{
  public function run()
  {
    $field = FieldType::where('code', 'text-3-2')->first();

    if ( $field ) {
        $field->markup = File::get( __DIR__.'/fields/'.'_housenumber.htm' );

        $field->save();
    }
  }
}
