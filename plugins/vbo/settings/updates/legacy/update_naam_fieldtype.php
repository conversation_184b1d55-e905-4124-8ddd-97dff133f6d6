<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class UpdateNaamFieldType extends Seeder
{
  public function run()
  {
    $field = FieldType::where('code', 'text-5')->first();

    if ( $field ) {
        $field->markup = File::get( __DIR__.'/fields/'.'_naam.htm' );

        $field->save();
    }
  }
}
