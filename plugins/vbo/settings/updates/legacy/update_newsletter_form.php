<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\Form;
use Renatio\FormBuilder\Models\Field;
use Renatio\FormBuilder\Models\FormImport;

class UpdateNewsletterForm extends Seeder
{
    public function run()
    {
        $forms = Form::all();
        $newsletterform = $forms->where('code', 'nieuwsbrief')->first();
        if ( $newsletterform ) {
            $newsletterformfields = Field::where('form_id', $newsletterform->id)->get();

            foreach ( $newsletterformfields as $field ) {
                $field->delete();
            }
            $path = __DIR__.'/data/newsletter-form.json';
            $importModel = new FormImport;
            $importModel->file_format = 'json';
            $importModel->importFile($path);
        }
    }
}
