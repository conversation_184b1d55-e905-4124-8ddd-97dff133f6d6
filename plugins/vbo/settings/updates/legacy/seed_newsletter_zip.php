<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class SeedNewsletterZipField extends Seeder
{
  public function run()
  {
    $path = __DIR__.'/fields/';
    $fieldtypes = FieldType::all();
    $seeding = $fieldtypes->where('code', 'postcode-nieuwsbrief')->first();

    if ( !$seeding ) {
      FieldType::create([
        'name' => 'Postcode nieuwsbrief',
        'code' => 'postcode-nieuwsbrief',
        'markup' => File::get($path.'_nieuwsbrief_postcode.htm'),
      ]);
    }
  }
}
