<?php namespace Vbo\Settings\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class CreateThankYouConnectionFields extends Migration
{
    public function up()
    {
        Schema::table('renatio_formbuilder_forms', function($table)
        {
            $table->string('ty_message_source')->nullable()->default('current');
            $table->string('ty_message_code')->nullable();
        });
    }

    public function down()
    {
        Schema::table('renatio_formbuilder_forms', function($table)
        {
            $table->dropColumn('ty_message_source');
            $table->dropColumn('ty_message_code');
        });
    }
}
