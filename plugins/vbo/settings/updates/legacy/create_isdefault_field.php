<?php namespace Vbo\Settings\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class CreateIsDefaultField extends Migration
{
    public function up()
    {
        Schema::table('renatio_formbuilder_field_types', function($table)
        {
            $table->boolean('is_default')->default(0);
        });
    }

    public function down()
    {
        Schema::table('renatio_formbuilder_field_types', function($table)
        {
            $table->dropColumn('is_default');
        });
    }
}
