<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class UpdateAdresVinderFieldType extends Seeder
{
  public function run()
  {
    $field = FieldType::where('code', 'adresvinder')->first();

    if ( $field ) {
        $field->markup = File::get( __DIR__.'/fields/'.'_adresvinder.htm' );

        $field->save();
    }
  }
}
