<?php

namespace Vbo\Settings\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class UpdateFormCustomFields extends Migration
{
  public function up()
    {
        Schema::table('renatio_formbuilder_forms', function($table)
        {
          $table->boolean('send_utm')->default(0)->change();
          $table->boolean('prelander')->default(0)->change();
          $table->boolean('nat_form')->default(0)->change();
          $table->boolean('collect_energylabel')->default(0)->change();
        });
    }
    
    public function down()
    {
        Schema::table('renatio_formbuilder_forms', function($table)
        {
            $table->integer('send_utm')->default(null)->change();
            $table->integer('prelander')->default(null)->change();
            $table->integer('nat_form')->default(null)->change();
            $table->integer('collect_energylabel')->default(null)->change();
        });
    }
}
