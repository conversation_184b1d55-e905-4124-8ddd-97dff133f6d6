<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class UpdateStreetCityFieldType extends Seeder
{
  public function run()
  {
    $street = FieldType::where('code', 'text-4')->first();
    $streetSuggestion = FieldType::where('code', 'text-4-3')->first();
    $city = FieldType::where('code', 'text-4-2')->first();
    $citySuggestion = FieldType::where('code', 'text-4-2-2')->first();

    if ( $street ) {
        $street->markup = File::get( __DIR__.'/fields/'.'_straatnaam.htm' );
        $street->save();
    }
    if ( $streetSuggestion ) {
        $streetSuggestion->markup = File::get( __DIR__.'/fields/'.'_straatnaam_suggesties.htm' );
        $streetSuggestion->save();
    }
    if ( $city ) {
        $city->markup = File::get( __DIR__.'/fields/'.'_woonplaats.htm' );
        $city->save();
    }
    if ( $citySuggestion ) {
        $citySuggestion->markup = File::get( __DIR__.'/fields/'.'_woonplaats_suggesties.htm' );
        $citySuggestion->save();
    }
  }
}
