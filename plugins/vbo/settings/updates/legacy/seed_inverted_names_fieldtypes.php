<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class SeedInvertedNamesFieldtypes extends Seeder
{
  public function run()
  {
    $path = __DIR__.'/fields/';
    $fieldtypes = FieldType::all();
    $lastnameInitials = $fieldtypes->where('code', 'lastname_initials')->first();
    $lastnameFirstnames = $fieldtypes->where('code', 'lastname_firstname')->first();

    if ( !$lastnameInitials ) {
      FieldType::create([
        'name' => 'Naam (achternaam - initialen)',
        'code' => 'lastname_initials',
        'markup' => File::get($path.'_achternaam_initialen.htm')
      ]);
    }
    if ( !$lastnameFirstnames ) {
        FieldType::create([
          'name' => 'Naam (achternaam - voornamen)',
          'code' => 'lastname_firstname',
          'markup' => File::get($path.'_achternaam_voornamen.htm')
        ]);
      }
  }
}
