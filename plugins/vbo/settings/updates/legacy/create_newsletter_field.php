<?php namespace Vbo\Settings\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class CreateNewsletterField extends Migration
{
    public function up()
    {
        Schema::table('renatio_formbuilder_forms', function($table)
        {
            $table->boolean('newsletter_signup')->default(false);
            $table->string('newsletter_signup_label')->nullable()->after('newsletter_signup');
        });
    }

    public function down()
    {
        Schema::table('renatio_formbuilder_forms', function($table)
        {
            $table->dropColumn('newsletter_signup');
            $table->dropColumn('newsletter_signup_label');
        });
    }
}
