<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class UpdateSiteLangFieldtypes extends Seeder
{
    public function run()
    {
        $zipcode = FieldType::where('code', 'text-3')->first();
        $zipcodeSug = FieldType::where('code', 'text-3-4')->first();
        $zipcodeNewsletter = FieldType::where('code', 'postcode-nieuwsbrief')->first();
        $zipcodeCity = FieldType::where('code', 'postcode-woonplaats')->first();
        $housenumber = FieldType::where('code', 'text-3-2')->first();
        $housenumberSug = FieldType::where('code', 'text-3-2-2')->first();
        $street = FieldType::where('code', 'text-4')->first();
        $streetSug = FieldType::where('code', 'text-4-3')->first();
        $city = FieldType::where('code', 'text-4-2')->first();
        $citySug = FieldType::where('code', 'text-4-2-2')->first();

        if ( $zipcode ) {
            $zipcode->markup = File::get( __DIR__.'/fields/'.'_postcode.htm' );
            $zipcode->save();
        }
        if ( $zipcodeSug ) {
            $zipcodeSug->markup = File::get( __DIR__.'/fields/'.'_postcode_sug.htm' );
            $zipcodeSug->save();
        }
        if ( $zipcodeNewsletter ) {
            $zipcodeNewsletter->markup = File::get( __DIR__.'/fields/'.'_nieuwsbrief_postcode.htm' );
            $zipcodeNewsletter->save();
        }
        if ( $zipcodeCity ) {
            $zipcodeCity->markup = File::get( __DIR__.'/fields/'.'_zipcode_city.htm' );
            $zipcodeCity->save();
        }
        if ( $housenumber ) {
            $housenumber->markup = File::get( __DIR__.'/fields/'.'_housenumber.htm' );
            $housenumber->save();
        }
        if ( $housenumberSug ) {
            $housenumberSug->markup = File::get( __DIR__.'/fields/'.'_housenumber_sug.htm' );
            $housenumberSug->save();
        }
        if ( $street ) {
            $street->markup = File::get( __DIR__.'/fields/'.'_straatnaam.htm' );
            $street->save();
        }
        if ( $streetSug ) {
            $streetSug->markup = File::get( __DIR__.'/fields/'.'_straatnaam_suggesties.htm' );
            $streetSug->save();
        }
        if ( $city ) {
            $city->markup = File::get( __DIR__.'/fields/'.'_woonplaats.htm' );
            $city->save();
        }
        if ( $citySug ) {
            $citySug->markup = File::get( __DIR__.'/fields/'.'_woonplaats_suggesties.htm' );
            $citySug->save();
        }
    }
}
