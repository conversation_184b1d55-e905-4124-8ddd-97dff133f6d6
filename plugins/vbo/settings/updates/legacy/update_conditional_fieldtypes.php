<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class UpdateConditionalFieldtypes extends Seeder
{
  public function run()
  {
    $dateField = FieldType::where('code', 'text-7-2')->first();
    $dropdown = FieldType::where('code', 'dropdown')->first();
    $dropdown_small = FieldType::where('code', 'dropdown-2')->first();
    $checkbox = FieldType::where('code', 'checkbox')->first();
    $checkbox_list = FieldType::where('code', 'checkbox_list')->first();
    $radio_list = FieldType::where('code', 'radio_list')->first();
    $textarea = FieldType::where('code', 'textarea')->first();
    $text = FieldType::where('code', 'text')->first();
    $section = FieldType::where('code', 'section')->first();

    if ( $dateField ) {
      $dateField->markup = File::get( __DIR__.'/fields/'.'_date.htm' );
      $dateField->save();
    }
    if ( $dropdown ) {
      $dropdown->markup = File::get( __DIR__.'/fields/'.'_dropdown.htm' );
      $dropdown->save();
    }
    if ( $dropdown_small ) {
      $dropdown_small->markup = File::get( __DIR__.'/fields/'.'_dropdown_small.htm' );
      $dropdown_small->save();
    }
    if ( $checkbox ) {
      $checkbox->markup = File::get( __DIR__.'/fields/'.'_checkbox.htm' );
      $checkbox->save();
    }
    if ( $checkbox_list ) {
      $checkbox_list->markup = File::get( __DIR__.'/fields/'.'_checkbox_list.htm' );
      $checkbox_list->save();
    }
    if ( $radio_list ) {
      $radio_list->markup = File::get( __DIR__.'/fields/'.'_radio_list.htm' );
      $radio_list->save();
    }
    if ( $textarea ) {
      $textarea->markup = File::get( __DIR__.'/fields/'.'_textarea.htm' );
      $textarea->save();
    }
    if ( $text ) {
      $text->markup = File::get( __DIR__.'/fields/'.'_text.htm' );
      $text->save();
    }
    if ( $section ) {
      $section->markup = File::get( __DIR__.'/fields/'.'_section.htm' );
      $section->save();
    }
    
  }
}
