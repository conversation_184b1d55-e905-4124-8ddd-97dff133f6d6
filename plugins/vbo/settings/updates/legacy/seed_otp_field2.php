<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class SeedOtpField2 extends Seeder
{
  public function run()
  {
    $path = __DIR__.'/fields/';
    $fieldtypes = FieldType::all();
    $seeding = $fieldtypes->where('code', 'telefoonnummer-otp')->first();

    if ( !$seeding ) {
      FieldType::create([
        'name' => 'Telefoonnummer + OTP',
        'code' => 'telefoonnummer-otp',
        'markup' => File::get($path.'_telefoonnummer_otp.htm'),
        'description' => 'Telefoonnummer met OTP'
      ]);
    }
  }
}
