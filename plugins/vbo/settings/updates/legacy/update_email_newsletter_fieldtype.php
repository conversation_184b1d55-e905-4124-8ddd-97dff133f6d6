<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class UpdateNewsletterEmailType extends Seeder
{
  public function run()
  {
    $field = FieldType::where('code', 'text-2-2')->first();

    if ( $field && $field->name === 'E-mailadres Nieuwsbrief') {
        $field->markup = File::get( __DIR__.'/fields/'.'_email_newsletter.htm' );

        $field->save();
    }
  }
}
