<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class UpdateUploadField extends Seeder
{
  public function run()
  {
    $field = FieldType::where('code', 'upload')->first();

    if ( $field ) {
        $field->markup = File::get( __DIR__.'/fields/'.'_upload.htm' );

        $field->save();
    }
  }
}
