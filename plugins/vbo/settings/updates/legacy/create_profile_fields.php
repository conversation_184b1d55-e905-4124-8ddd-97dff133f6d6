<?php namespace Vbo\Settings\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class createProfileFieldsVboSettings extends Migration
{
    public function up()
    {
        Schema::table('backend_users', function($table)
        {
            $table->string('biography', 511)->nullable();
            $table->text('social')->nullable();
        });
    }

    public function down()
    {
        Schema::table('backend_users', function($table)
        {
            $table->dropColumn('biography');
            $table->dropColumn('social');
        });
    }
}
