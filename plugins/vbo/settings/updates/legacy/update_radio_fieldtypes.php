<?php

namespace Vbo\Settings\Updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class UpdateRadioField extends Seeder
{
  public function run()
  {
    $radioList = FieldType::where('code', 'radio_list')->first();

    if ( $radioList ) {
        $radioList->markup = File::get( __DIR__.'/fields/'.'_radio_list.htm' );
        $radioList->save();
    }
  }
}
