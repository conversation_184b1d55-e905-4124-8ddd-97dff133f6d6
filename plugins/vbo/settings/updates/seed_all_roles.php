<?php namespace Vbo\Settings\Updates;

use October\Rain\Database\Updates\Seeder;
use Backend\Models\UserRole;
use Backend\Models\User;

/**
 * Consolidated roles seeder for VBO Settings Plugin v2.0
 * This seeder includes all role creations and updates from versions 1.0.1 through 1.3.3
 */
class SeedAllRoles extends Seeder
{
    public function run()
    {
        // Create or update Webmaster role
        $this->createWebmasterRole();
        
        // Create or update Admin role
        $this->createAdminRole();
        
        // Clean up backend users and set roles
        $this->cleanupBackendUsers();
    }
    
    private function createWebmasterRole()
    {
        $webmasterRole = UserRole::where('code', 'webmaster')->first();
        
        if (!$webmasterRole) {
            $webmasterRole = UserRole::create([
                'name' => 'Website beheerder',
                'code' => 'webmaster',
                'description' => 'Beheerders rol voor webmasters',
                'permissions' => $this->getWebmasterPermissions()
            ]);
        } else {
            // Update permissions for existing role
            $webmasterRole->permissions = $this->getWebmasterPermissions();
            $webmasterRole->save();
        }
    }
    
    private function createAdminRole()
    {
        $adminRole = UserRole::where('code', 'admin')->first();
        
        if (!$adminRole) {
            UserRole::create([
                'name' => 'Administrator',
                'code' => 'admin',
                'description' => 'Volledige beheerders rol',
                'permissions' => $this->getAdminPermissions()
            ]);
        }
    }
    
    private function cleanupBackendUsers()
    {
        // Delete specific backend user if exists (from delete_backenduser.php)
        $userToDelete = User::where('email', '<EMAIL>')->first();
        if ($userToDelete) {
            $userToDelete->delete();
        }
        
        // Update Thomas account (from update_thomas_account.php)
        $thomasUser = User::where('login', 'thomas')->first();
        if ($thomasUser) {
            $thomasUser->email = '<EMAIL>';
            $thomasUser->save();
        }
        
        // Set roles for backend users (from set_role_backendusers.php)
        $webmasterRole = UserRole::where('code', 'webmaster')->first();
        if ($webmasterRole) {
            $users = User::whereDoesntHave('role')->get();
            foreach ($users as $user) {
                $user->role_id = $webmasterRole->id;
                $user->save();
            }
        }
    }
    
    private function getWebmasterPermissions()
    {
        return [
            "general.view_offline" => "1",
            "general.backend" => "1",
            "general.backend.view_offline" => "1",
            "dashboard" => "1",
            "rainlab.pages.manage_pages" => "1",
            "rainlab.pages.manage_menus" => "1",
            "rainlab.pages.manage_content" => "1",
            "mail.templates" => "1",
            "mail.settings" => "1",
            "cms.themes" => "1",
            "cms.theme_customize" => "1",
            "cms.maintenance_mode" => "1",
            "media.library" => "1",
            "media.library.create" => "1",
            "media.library.delete" => "1",
            "utilities.logs" => "1",
            "settings.manage_sites" => "1",
            "preferences" => "1",
            "albrightlabs.redirects.manage_redirects" => "1",
            "janvince.smallgdpr.access_cookies_settings" => "1",
            "netsetters.klantenvertellen.access_settings" => "1",
            "rainlab.location.access_settings" => "1",
            "rainlab.sitemap.access_definitions" => "1",
            "rainlab.translate.manage_messages" => "1",
            "renatio.formbuilder.access_forms" => "1",
            "renatio.formbuilder.access_forms.create" => "1",
            "renatio.formbuilder.access_forms.update" => "1",
            "renatio.formbuilder.access_forms.delete" => "1",
            "renatio.formbuilder.access_forms.import_export" => "1",
            "renatio.formbuilder.access_form_logs" => "1",
            "renatio.formbuilder.access_form_logs.preview" => "1",
            "renatio.formbuilder.access_form_logs.truncate" => "1",
            "renatio.formbuilder.access_form_logs.delete" => "1",
            "renatio.formbuilder.access_form_logs.export" => "1",
            "renatio.formbuilder.access_settings" => "1",
            "manage_plugin" => "1",
            "offline.boxes.manage_settings" => "1",
            "offline.boxes.access_editor" => "1",
            // OTP permissions (from update_webmaster_role updates)
            "renatio.formbuilder.access_otp" => "1",
            "renatio.formbuilder.access_otp.create" => "1",
            "renatio.formbuilder.access_otp.update" => "1",
            "renatio.formbuilder.access_otp.delete" => "1"
        ];
    }
    
    private function getAdminPermissions()
    {
        return [
            "general.view_offline" => "1",
            "general.backend" => "1",
            "general.backend.view_offline" => "1",
            "dashboard" => "1",
            "system.manage_updates" => "1",
            "system.manage_plugins" => "1",
            "system.manage_themes" => "1",
            "system.manage_mail_settings" => "1",
            "system.manage_mail_templates" => "1",
            "system.manage_preferences" => "1",
            "system.manage_editor" => "1",
            "system.manage_branding" => "1",
            "system.manage_logs" => "1",
            "backend.manage_users" => "1",
            "backend.manage_preferences" => "1",
            "backend.manage_default_dashboard" => "1",
            "backend.manage_branding" => "1"
        ];
    }
}
