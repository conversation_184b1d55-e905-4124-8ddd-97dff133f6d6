# Legacy Updates Mapping - VBO Settings Plugin

Dit document toont hoe alle 103 legacy update bestanden zijn geconsoli<PERSON>erd in versie 2.0.

## Schema Wijzigingen → `create_consolidated_schema.php`

### FormBuilder Fields Table
- `create_cl_fields.php` → Conditional logic velden
- `create_description_field_formbuilder.php` → Description veld
- `create_range_field_formbuilder.php` → Range veld
- `create_offer_example_field_formbuilder.php` → Offer example veld
- `create_required_field_formbuilder.php` → Required field veld
- `create_hide_label_formbuilder.php` → Hide label veld
- `create_custom_validation.php` → Custom validation veld

### FormBuilder Forms Table
- `create_pixels_field_formbuilder.php` → Conversion pixels
- `create_utm_field_formbuilder.php` → UTM source veld
- `create_lang_field_formbuilder.php` → Language veld
- `create_postback_field_formbuilder.php` → Postback veld
- `create_loading_field_formbuilder.php` → Loading velden
- `create_prelander_field_formbuilder.php` → Prelander veld
- `create_ty_field_formbuilder.php` → Thank you message veld
- `create_energylabel_field.php` → Energy label veld
- `create_newsletter_field.php` → Newsletter signup veld
- `create_newsletter_field_2.php` → Newsletter tooltip veld
- `create_newsletter_checkbox.php` → Newsletter checkbox veld
- `create_thankyou_connection.php` → Thank you connection veld
- `create_custom_options_fields.php` → Custom options veld

### Field Types Table
- `create_isdefault_field.php` → Is default veld

### Backend Users Table
- `create_profile_fields.php` → Biography en social velden

### Missing Fields
- `create_missing_fields.php` → Diverse ontbrekende velden
- `create_missing_fields_2.php` → Aanvullende ontbrekende velden

## Field Types → `seed_all_fieldtypes.php`

### Alert/Warning Fields
- `seed_alert_fieldtype.php` → Melding blok
- `seed_waarschuwing_fieldtype.php` → Waarschuwing veld

### Address/Location Fields
- `seed_adresvinder_fieldtype.php` → Adresvinder
- `seed_be_zipcode_fields.php` → België postcode velden
- `enable_zipcode_be_fieldtype.php` → België postcode activatie
- `seed_street_dropdown.php` → Straat dropdown
- `seed_street_dropdown_field.php` → Straat dropdown veld
- `seed_newsletter_zip.php` → Nieuwsbrief postcode

### Phone/Contact Fields
- `seed_otp_field.php` → OTP veld (eerste versie)
- `seed_otp_field2.php` → OTP veld (tweede versie)
- `seed_otp_2_fieldtype.php` → OTP 2 fieldtype
- `seed_intphone_field.php` → Internationaal telefoonnummer

### Name Fields
- `seed_firstname_lastname.php` → Voornaam achternaam
- `seed_inverted_names_fieldtypes.php` → Omgekeerde naam velden

### Special Fields
- `seed_hidden_fieldtype.php` → Verborgen veld
- `seed_text_number_fieldtype.php` → Text number veld
- `seed_begin_knop_fieldtype.php` → Begin knop
- `seed_hypotheekrente_fields.php` → Hypotheekrente velden
- `seed_prelander_radio_list.php` → Prelander radio lijst

## Roles & Users → `seed_all_roles.php`

### Role Creation
- `seed_webmaster_role.php` → Webmaster rol
- `seed_admin_role.php` → Admin rol

### Role Updates
- `update_webmaster_role.php` → Webmaster rol update 1
- `update_webmaster_role_2.php` → Webmaster rol update 2
- `update_webmaster_role_3.php` → Webmaster rol update 3
- `update_webmaster_role_4.php` → Webmaster rol update 4

### User Management
- `delete_backenduser.php` → Backend gebruiker verwijdering
- `set_role_backendusers.php` → Rol toewijzingen
- `update_thomas_account.php` → Thomas account update

## Configuraties → `set_all_configurations.php`

### Cookie Settings
- `set_cookie_settings.php` → Cookie instellingen 1
- `set_cookie_settings_2.php` → Cookie instellingen 2
- `set_cookie_settings_3.php` → Cookie instellingen 3

### Form Updates
- `update_default_forms.php` → Standaard formulieren 1
- `update_default_forms_2.php` → Standaard formulieren 2
- `update_default_forms_3.php` → Standaard formulieren 3
- `update_newsletter_form.php` → Nieuwsbrief formulier

## Field Type Updates (Geïntegreerd in seed_all_fieldtypes.php)

### Phone Number Updates
- `update_telefoonnummer_fieldtype.php` → Telefoonnummer update
- `update_otp_field.php` → OTP veld update 1
- `update_otp_field2.php` → OTP veld update 2
- `update_otp_field3.php` → OTP veld update 3
- `update_otp_field4.php` → OTP veld update 4
- `update_otp_field5.php` → OTP veld update 5
- `update_otp_field6.php` → OTP veld update 6
- `update_otp_fields.php` → OTP velden update
- `update_otp_2_field.php` → OTP 2 veld updates (3x)

### Address Updates
- `update_streetcity_fieldtype.php` → Straat/stad update 1
- `update_streetcity_fieldtype_2.php` → Straat/stad update 2
- `update_streetcity_fieldtype_3.php` → Straat/stad update 3
- `update_housenumber_fieldtype.php` → Huisnummer update
- `update_postcode_sug_fieldtype.php` → Postcode suggesties
- `update_zipcode_fieldtype.php` → Postcode validatie
- `update_zipcode_be_fieldtype.php` → België postcode 1
- `update_zipcode_be_fieldtype-2.php` → België postcode 2
- `update_zipcode_be_fieldtype3.php` → België postcode 3

### Form Control Updates
- `update_submit_field.php` → Submit knop update 1
- `update_submit_field_2.php` → Submit knop update 2
- `update_upload_field.php` → Upload veld updates (2x)
- `update_section_fieldtype.php` → Sectie veld update
- `update_begin_knop.php` → Begin knop update

### Multi-option Field Updates
- `update_checkboxlist.php` → Checkbox lijst update 1
- `update_checkboxlist_2.php` → Checkbox lijst update 2
- `update_radiolist.php` → Radio lijst update 1
- `update_radiolist-2.php` → Radio lijst update 2
- `update_radiolist-3.php` → Radio lijst update 3
- `update_radiolist-4.php` → Radio lijst update 4
- `update_radiolist-5.php` → Radio lijst update 5
- `update_radio_fieldtypes.php` → Radio veld types
- `update_prelander_radio_fieldtype.php` → Prelander radio

### Specialized Field Updates
- `update_alert_fieldtype.php` → Alert veld update 1
- `update_alert_fieldtype_2.php` → Alert veld update 2
- `update_alert_fieldtype_3.php` → Alert veld update 3
- `update_dob_alt_fieldtype.php` → Geboortedatum alternatief
- `update_slider_bedrag.php` → Slider bedrag 1
- `update_slider_bedrag2.php` → Slider bedrag 2
- `update_naam_fieldtype.php` → Naam veld type
- `update_date_fieldtype.php` → Datum veld updates (2x)
- `update_email_field.php` → E-mail veld updates (3x)
- `update_email_newsletter_fieldtype.php` → E-mail nieuwsbrief updates (2x)
- `update_newsletter_zipcode_fieldtype.php` → Nieuwsbrief postcode
- `update_newsletters_zip_fieldtype.php` → Nieuwsbrief zip
- `update_adresvinder_fieldtype.php` → Adresvinder updates (3x)

### Bulk Field Updates
- `update_fieldtype_labels.php` → Veld type labels
- `update_fieldtypes_3_7.php` → Maart 7 update 1
- `update_fieldtypes_3_7_2.php` → Maart 7 update 2
- `update_fieldtypes_19_12.php` → December 19 update
- `update_fieldtypes_disable_trans.php` → Vertaling uitschakelen
- `update_sitelang_fieldtypes.php` → Site taal veld types
- `update_raw_labels.php` → Raw labels update
- `update_conditional_fieldtypes.php` → Conditionele logica
- `update_conlogic_fieldtypes.php` → Conditionele logica update
- `update_form_customfields.php` → Formulier custom velden

## Totaal: 103 → 4 bestanden

Alle functionaliteit is behouden, maar nu georganiseerd in 4 logische, onderhoudbare bestanden.
