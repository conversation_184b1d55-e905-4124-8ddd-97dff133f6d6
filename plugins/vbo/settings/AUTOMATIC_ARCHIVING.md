# Handmatige Archivering - VBO Settings Plugin v2.0

## Overzicht

Versie 2.0 van de VBO Settings plugin bevat een handmatig archivering script dat alle legacy update bestanden kan opruimen na de upgrade.

## Hoe het werkt

### 🔄 **Handmatige uitvoering**
- Wordt handmatig uitgevoerd na de 2.0 upgrade
- Volledige controle voor de administrator
- Voorkomt merge conflicten en deployment problemen

### 🔍 **Intelligente detectie**
```php
// Detecteert of dit een upgrade is door te zoeken naar legacy bestanden
$legacyFiles = glob($updatesDir . '/create_*.php');
$legacyFiles = array_merge($legacyFiles, glob($updatesDir . '/seed_*.php'));
// etc...

// Als geen legacy bestanden gevonden, skip archivering (fresh install)
if (empty($legacyFiles)) {
    return;
}
```

### 📁 **Veilige archivering**
- Verplaatst bestanden naar `/updates/legacy/` directory
- Behoudt alle bestanden voor rollback mogelijkheden
- Maakt automatisch documentatie aan

### 🛡️ **Veiligheidscontroles**
```php
// Check of archivering al heeft plaatsgevonden
if (is_dir($legacyDir)) {
    echo "Archivering al uitgevoerd. Opnieuw uitvoeren? (y/N): ";
    // Vraagt bevestiging van gebruiker
}
```
- Voorkomt ongewenste dubbele archivering
- Gebruiker heeft volledige controle over het proces

## Wat wordt gearchiveerd

### ✅ **Gearchiveerd**
- `create_*.php` (behalve `create_consolidated_schema.php`)
- `seed_*.php` (behalve nieuwe geconsolideerde bestanden)
- `update_*.php` (alle update bestanden)
- `set_*.php` (behalve `set_all_configurations.php`)
- `delete_*.php` (alle delete bestanden)

### ❌ **NIET gearchiveerd**
- `version.yaml`
- `create_consolidated_schema.php`
- `seed_all_fieldtypes.php`
- `seed_all_roles.php`
- `set_all_configurations.php`
- `fields/` directory
- `data/` directory

## Output en feedback

### 📊 **Console output**
```
=== ARCHIVERING VOLTOOID ===
Gearchiveerde bestanden: 103
Behouden bestanden: 7
Legacy bestanden zijn verplaatst naar: /path/to/updates/legacy
README.md aangemaakt in legacy directory
```

### ⚠️ **Waarschuwingen**
```
=== ARCHIVERING AL UITGEVOERD ===
De legacy directory bestaat al: /path/to/updates/legacy
Dit betekent dat de automatische archivering tijdens de 2.0 upgrade al heeft plaatsgevonden.
```

## Handmatige controle

### 🔍 **Verificatie na upgrade**
```bash
# Check of archivering succesvol was
ls -la plugins/vbo/settings/updates/legacy/

# Check logs
tail -f storage/logs/system.log | grep "VBO Settings"
```

### 🔄 **Archivering uitvoeren**
```bash
cd plugins/vbo/settings/
php archive_legacy_updates.php
```

## Rollback procedure

Als er problemen zijn na de upgrade:

```bash
# 1. Stop de website (maintenance mode)
php artisan down

# 2. Verplaats legacy bestanden terug
mv plugins/vbo/settings/updates/legacy/* plugins/vbo/settings/updates/

# 3. Verwijder geconsolideerde bestanden
rm plugins/vbo/settings/updates/create_consolidated_schema.php
rm plugins/vbo/settings/updates/seed_all_fieldtypes.php
rm plugins/vbo/settings/updates/seed_all_roles.php
rm plugins/vbo/settings/updates/set_all_configurations.php

# 4. Reset version.yaml naar 1.3.3
# Edit version.yaml en verwijder de 2.0.0 entry

# 5. Clear cache en restart
php artisan cache:clear
php artisan up
```

## Voordelen

1. **🎯 Controle**: Volledige controle over wanneer archivering plaatsvindt
2. **🛡️ Veilig**: Bestanden worden bewaard, niet verwijderd
3. **📝 Gedocumenteerd**: Automatische README generatie
4. **🔍 Transparant**: Duidelijke feedback over alle acties
5. **🔄 Rollback**: Eenvoudig terug te draaien
6. **⚡ Geen conflicten**: Voorkomt merge conflicten en deployment problemen

Dit systeem zorgt ervoor dat de overgang naar versie 2.0 volledig onder controle en veilig verloopt!
