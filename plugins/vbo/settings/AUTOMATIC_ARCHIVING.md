# Automatische Archivering - VBO Settings Plugin v2.0

## Overzicht

Versie 2.0 van de VBO Settings plugin bevat een automatisch archivering systeem dat alle legacy update bestanden opruimt tijdens de upgrade.

## Hoe het werkt

### 🔄 **Automatische uitvoering**
- Wordt automatisch uitgevoerd tijdens de 2.0 database migration
- Geen handmatige actie vereist van de administrator
- Werkt alleen bij upgrades, niet bij fresh installs

### 🔍 **Intelligente detectie**
```php
// Detecteert of dit een upgrade is door te zoeken naar legacy bestanden
$legacyFiles = glob($updatesDir . '/create_*.php');
$legacyFiles = array_merge($legacyFiles, glob($updatesDir . '/seed_*.php'));
// etc...

// Als geen legacy bestanden gevonden, skip archivering (fresh install)
if (empty($legacyFiles)) {
    return;
}
```

### 📁 **Veilige archivering**
- Verplaatst bestanden naar `/updates/legacy/` directory
- Behoudt alle bestanden voor rollback mogelijkheden
- Maakt automatisch documentatie aan

### 🛡️ **Foutafhandeling**
```php
try {
    $this->archiveLegacyUpdates();
} catch (\Exception $e) {
    Log::warning("Legacy archiving failed but continuing with migration");
}
```
- Database migration faalt nooit door archivering problemen
- Alle fouten worden gelogd maar blokkeren de upgrade niet

## Wat wordt gearchiveerd

### ✅ **Gearchiveerd**
- `create_*.php` (behalve `create_consolidated_schema.php`)
- `seed_*.php` (behalve nieuwe geconsolideerde bestanden)
- `update_*.php` (alle update bestanden)
- `set_*.php` (behalve `set_all_configurations.php`)
- `delete_*.php` (alle delete bestanden)

### ❌ **NIET gearchiveerd**
- `version.yaml`
- `create_consolidated_schema.php`
- `seed_all_fieldtypes.php`
- `seed_all_roles.php`
- `set_all_configurations.php`
- `fields/` directory
- `data/` directory

## Logging en monitoring

### 📊 **Success logging**
```
VBO Settings Plugin v2.0: Automatically archived 103 legacy update files to /updates/legacy/
```

### ⚠️ **Warning logging**
```
VBO Settings Plugin v2.0: Could not create legacy directory for archiving. Continuing without archiving.
VBO Settings Plugin v2.0: Legacy archiving failed but continuing with migration: [error details]
```

## Handmatige controle

### 🔍 **Verificatie na upgrade**
```bash
# Check of archivering succesvol was
ls -la plugins/vbo/settings/updates/legacy/

# Check logs
tail -f storage/logs/system.log | grep "VBO Settings"
```

### 🔄 **Handmatige archivering (indien nodig)**
```bash
cd plugins/vbo/settings/
php archive_legacy_updates.php
```

## Rollback procedure

Als er problemen zijn na de upgrade:

```bash
# 1. Stop de website (maintenance mode)
php artisan down

# 2. Verplaats legacy bestanden terug
mv plugins/vbo/settings/updates/legacy/* plugins/vbo/settings/updates/

# 3. Verwijder geconsolideerde bestanden
rm plugins/vbo/settings/updates/create_consolidated_schema.php
rm plugins/vbo/settings/updates/seed_all_fieldtypes.php
rm plugins/vbo/settings/updates/seed_all_roles.php
rm plugins/vbo/settings/updates/set_all_configurations.php

# 4. Reset version.yaml naar 1.3.3
# Edit version.yaml en verwijder de 2.0.0 entry

# 5. Clear cache en restart
php artisan cache:clear
php artisan up
```

## Voordelen

1. **🚀 Automatisch**: Geen handmatige actie vereist
2. **🛡️ Veilig**: Bestanden worden bewaard, niet verwijderd
3. **📝 Gedocumenteerd**: Automatische README generatie
4. **🔍 Traceerbaar**: Volledige logging van alle acties
5. **🔄 Rollback**: Eenvoudig terug te draaien
6. **⚡ Performant**: Geen impact op database migration snelheid

Dit systeem zorgt ervoor dat de overgang naar versie 2.0 volledig transparant en veilig verloopt!
