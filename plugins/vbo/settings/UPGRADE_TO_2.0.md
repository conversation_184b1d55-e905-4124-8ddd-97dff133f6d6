# VBO Settings Plugin - Upgrade naar versie 2.0

## Overzicht

De VBO Settings plugin had een groot aantal incrementele updates (van versie 1.0.1 tot 1.3.3) die elk kleine wijzigingen aanbrachten. Versie 2.0 consolideert al deze updates in 4 hoofdbestanden voor een veel schonere installatie.

## Wat is er geconsolideerd?

### Versie 1.x (103 update bestanden)
- 103 individuele update bestanden
- Veel overlappende functionaliteit
- Complexe installatie bij nieuwe deployments
- Moeilijk te onderhouden

### Versie 2.0 (4 geconsolideerde bestanden)
- `create_consolidated_schema.php` - Alle database schema wijzigingen
- `seed_all_fieldtypes.php` - Alle fieldtype definities
- `seed_all_roles.php` - Alle gebruikersrollen en permissies
- `set_all_configurations.php` - Alle configuratie instellingen

## Geconsolideerde functionaliteit

### Database Schema (`create_consolidated_schema.php`)
- **FormBuilder Fields**: Conditional logic, descriptions, validatie, custom options
- **FormBuilder Forms**: Conversion pixels, UTM tracking, newsletter signup, thank you messages
- **Field Types**: Default flags en configuraties
- **Backend Users**: Biography en social media velden

### Field Types (`seed_all_fieldtypes.php`)
- **Adres velden**: Adresvinder, postcode suggesties, straat suggesties, huisnummer
- **Contact velden**: Telefoonnummer met OTP, internationaal telefoonnummer
- **Naam velden**: Verschillende naam formaten (voornaam/achternaam combinaties)
- **Speciale velden**: Verborgen velden, text-number, begin knop
- **Locatie specifiek**: België postcode velden, gemeente velden
- **Financieel**: Hypotheekrente velden, slider bedrag velden
- **Waarschuwingen**: Alert blokken, waarschuwing velden

### Gebruikersrollen (`seed_all_roles.php`)
- **Webmaster rol**: Volledige website beheer permissies
- **Admin rol**: Systeem beheer permissies
- **Gebruiker cleanup**: Verwijdering van oude accounts, rol toewijzingen

### Configuraties (`set_all_configurations.php`)
- **Cookie instellingen**: GDPR compliance configuratie
- **Standaard formulieren**: E-mail templates en instellingen
- **Field type configuraties**: Namen en gedrag van veld types

## Voordelen van versie 2.0

1. **Eenvoudige installatie**: 1 grote update in plaats van 103 kleine
2. **Betere prestaties**: Minder database queries tijdens installatie
3. **Makkelijker onderhoud**: Overzichtelijke code structuur
4. **Minder fouten**: Geen afhankelijkheden tussen updates
5. **Snellere deployment**: Vooral bij nieuwe installaties

## Installatie instructies

### Voor nieuwe installaties
Installeer gewoon de plugin - versie 2.0 wordt automatisch uitgevoerd.

### Voor bestaande installaties
De plugin detecteert automatisch welke updates al zijn uitgevoerd en voert alleen de ontbrekende wijzigingen uit.

## Handmatige archivering van legacy bestanden

### Waarom handmatig?
Na de upgrade naar versie 2.0 kun je optioneel de oude update bestanden archiveren. Dit wordt **niet automatisch** gedaan om:
- Merge conflicten te voorkomen
- Volledige controle te behouden over file management
- Compatibiliteit met verschillende deployment workflows te garanderen

### Hoe archiveren
Na een succesvolle upgrade naar versie 2.0:

```bash
cd plugins/vbo/settings/
php archive_legacy_updates.php
```

### Wat het script doet
1. **Detectie**: Controleert of er legacy update bestanden aanwezig zijn
2. **Archivering**: Verplaatst alle oude update bestanden naar `/updates/legacy/`
3. **Documentatie**: Maakt automatisch een README.md aan met details
4. **Veiligheid**: Vraagt bevestiging als archivering al eerder is uitgevoerd

### Wat wordt gearchiveerd
- Alle `create_*.php` bestanden (behalve `create_consolidated_schema.php`)
- Alle `seed_*.php` bestanden (behalve de nieuwe geconsolideerde bestanden)
- Alle `update_*.php` bestanden
- Alle `set_*.php` bestanden (behalve `set_all_configurations.php`)
- Alle `delete_*.php` bestanden

### Wat wordt NIET gearchiveerd
- `version.yaml` - Versie configuratie
- `create_consolidated_schema.php` - Nieuwe schema migration
- `seed_all_fieldtypes.php` - Nieuwe fieldtype seeder
- `seed_all_roles.php` - Nieuwe roles seeder
- `set_all_configurations.php` - Nieuwe configuratie seeder
- `fields/` directory - Template bestanden
- `data/` directory - Data bestanden

### Veiligheid
- Legacy bestanden worden **verplaatst**, niet verwijderd
- Volledige rollback mogelijk
- Automatische documentatie van gearchiveerde bestanden

## Backward compatibility

Versie 2.0 is volledig backward compatible. Alle bestaande functionaliteit blijft werken zoals verwacht.

## Technische details

### Schema wijzigingen
Alle schema wijzigingen gebruiken `Schema::hasColumn()` checks om dubbele kolommen te voorkomen.

### Field type updates
Field types worden bijgewerkt als ze al bestaan, of aangemaakt als ze nieuw zijn.

### Configuratie updates
Configuraties worden alleen ingesteld als ze nog niet bestaan of als ze moeten worden bijgewerkt.

## Ondersteuning

Voor vragen over de upgrade naar versie 2.0, neem contact op met het development team.
