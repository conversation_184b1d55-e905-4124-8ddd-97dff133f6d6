<?php
/**
 * <PERSON>ript om legacy update bestanden te archiveren na upgrade naar versie 2.0
 * 
 * Dit script kan worden uitgevoerd om alle oude update bestanden naar een 
 * legacy map te verplaatsen nadat versie 2.0 succesvol is geïnstalleerd.
 * 
 * Gebruik: php archive_legacy_updates.php
 */

$updatesDir = __DIR__ . '/updates';
$legacyDir = __DIR__ . '/updates/legacy';

// Maak legacy directory aan als deze niet bestaat
if (!is_dir($legacyDir)) {
    mkdir($legacyDir, 0755, true);
    echo "Legacy directory aangemaakt: $legacyDir\n";
}

// Lijst van bestanden die NIET gearchiveerd moeten worden (versie 2.0 bestanden)
$keepFiles = [
    'version.yaml',
    'create_consolidated_schema.php',
    'seed_all_fieldtypes.php', 
    'seed_all_roles.php',
    'set_all_configurations.php',
    'fields', // directory
    'data'    // directory
];

// Scan de updates directory
$files = scandir($updatesDir);

$archivedCount = 0;
$skippedCount = 0;

foreach ($files as $file) {
    // Skip . en .. directories
    if ($file === '.' || $file === '..') {
        continue;
    }
    
    // Skip bestanden die we willen behouden
    if (in_array($file, $keepFiles)) {
        echo "Behouden: $file\n";
        $skippedCount++;
        continue;
    }
    
    $sourcePath = $updatesDir . '/' . $file;
    $targetPath = $legacyDir . '/' . $file;
    
    // Verplaats het bestand
    if (rename($sourcePath, $targetPath)) {
        echo "Gearchiveerd: $file\n";
        $archivedCount++;
    } else {
        echo "FOUT: Kon $file niet archiveren\n";
    }
}

echo "\n=== ARCHIVERING VOLTOOID ===\n";
echo "Gearchiveerde bestanden: $archivedCount\n";
echo "Behouden bestanden: $skippedCount\n";
echo "Legacy bestanden zijn verplaatst naar: $legacyDir\n";

// Maak een README bestand in de legacy directory
$readmeContent = "# Legacy Update Bestanden

Deze directory bevat alle legacy update bestanden van VBO Settings plugin versies 1.0.1 tot 1.3.3.

Deze bestanden zijn gearchiveerd na de upgrade naar versie 2.0 en zijn niet meer nodig voor de werking van de plugin.

## Waarom gearchiveerd?

Versie 2.0 consolideert alle functionaliteit van deze 103+ individuele update bestanden in 4 geconsolideerde bestanden:

- `create_consolidated_schema.php` - Alle database schema wijzigingen
- `seed_all_fieldtypes.php` - Alle fieldtype definities  
- `seed_all_roles.php` - Alle gebruikersrollen en permissies
- `set_all_configurations.php` - Alle configuratie instellingen

## Veiligheid

Deze bestanden kunnen veilig worden verwijderd, maar worden bewaard voor:
- Referentie doeleinden
- Debugging van legacy functionaliteit
- Rollback scenario's (indien nodig)

## Gearchiveerd op

" . date('Y-m-d H:i:s') . "

## Totaal aantal gearchiveerde bestanden

$archivedCount bestanden
";

file_put_contents($legacyDir . '/README.md', $readmeContent);
echo "README.md aangemaakt in legacy directory\n";

echo "\n=== KLAAR ===\n";
echo "De plugin gebruikt nu alleen nog de 4 geconsolideerde update bestanden van versie 2.0.\n";
echo "Legacy bestanden zijn veilig gearchiveerd en kunnen indien gewenst worden verwijderd.\n";
?>
